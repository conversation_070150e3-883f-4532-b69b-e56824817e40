import React from 'react';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import '../styles/NFTTrade.css';

const NFTTrade = () => {
  // Real NFT collection data with current market stats
  const topCollections = [
    {
      rank: 1,
      name: 'Bored Ape Yacht Club',
      image: 'https://i.seadn.io/gae/Ju9CkWtV-1Okvf45wo8UctR-M9He2PjILP0oOvxE89AyiPPGtrR3gysu1Zgy0hjd2xKIgjJJtWIc0ybj4Vd7wv8t3pxDGHoJBzDB?auto=format&dpr=1&w=384',
      volume: '11.35',
      change: '+2.4%',
      verified: true,
      changeType: 'positive'
    },
    {
      rank: 2,
      name: 'CryptoPunks',
      image: 'https://i2.seadn.io/ethereum/41f13fa2cbb64d908a9add3243345bbc/f092b6ebf5bbfaca498a6135cad5a8/4bf092b6ebf5bbfaca498a6135cad5a8.png?w=100&h=100&fit=crop',
      volume: '41.89',
      change: '-0.21%',
      verified: true,
      changeType: 'negative'
    },
    {
      rank: 3,
      name: 'Pudgy Penguins',
      image: 'https://i.seadn.io/gae/yNi-XdGxsgQCPpqSio4o31ygAV6wURdIdInWRcFIl46UjUQ1eV7BEndGe8L661OoG-clRi7EgInLX4LPu9Jfw4fq0bnVYHqg7RFi?auto=format&dpr=1&w=384',
      volume: '14.35',
      change: '+8.7%',
      verified: true,
      changeType: 'positive'
    },
    {
      rank: 4,
      name: 'Azuki',
      image: 'https://i.seadn.io/gae/H8jOCJuQokNqGBpkBN5wk1oZwO7LM8bNnrHCaekV2nKjnCqw6UB5oaH8XyNeBDj6bA_n1mjejzhFQUP3O1NfjFLHr3FOaeHcTOOT?auto=format&dpr=1&w=384',
      volume: '7.82',
      change: '+15.3%',
      verified: true,
      changeType: 'positive'
    },
    {
      rank: 5,
      name: 'Mutant Ape Yacht Club',
      image: 'https://i.seadn.io/gae/lHexKRMpw-aoSyB1WdFBff5yfANLReFxHzt1DOj_sg7mS14yARpuvYcUtsyyx-Nkpk6WTcUPFoG53VnLJezYi8hAs0OxNZwlw6Y-dmI?w=100&h=100&fit=crop',
      volume: '3.45',
      change: '+4.2%',
      verified: true,
      changeType: 'positive'
    },
    {
      rank: 6,
      name: 'Doodles',
      image: 'https://i.seadn.io/gae/7B0qai02OdHA8P_EOVK672qUliyjQdQDGNrACxs7WnTgZAkJa_wWURnIFKeOh5VTf8cfTqW3wQpozGedaC9mteKphEOtztls02RlWQ?auto=format&dpr=1&w=384',
      volume: '2.89',
      change: '+12.1%',
      verified: true,
      changeType: 'positive'
    },
    {
      rank: 7,
      name: 'Art Blocks Curated',
      image: 'https://images.unsplash.com/photo-1634017839464-5c339ebe3cb4?w=100&h=100&fit=crop&crop=center',
      volume: '5.67',
      change: '-2.8%',
      verified: true,
      changeType: 'negative'
    },
    {
      rank: 8,
      name: 'Clone X',
      image: 'https://i2.seadn.io/ethereum/75db99053ab444cfa0d8fb55d1f60547/2a98bfb35de339b49f3a4f0c4696c4/5c2a98bfb35de339b49f3a4f0c4696c4.png?w=100&h=100&fit=crop',
      volume: '4.23',
      change: '+6.9%',
      verified: true,
      changeType: 'positive'
    },
    {
      rank: 9,
      name: 'The Sandbox',
      image: 'https://i.seadn.io/gae/SXH8tW1siikJjhZHaYvTJF_UMw_TLGm_V_h_GzKOwBNZ-BaJBJGGDWWGNKhqZafrZcUKPS_XLQSBc5O_kBHHYA?auto=format&dpr=1&w=384',
      volume: '1.95',
      change: '+18.4%',
      verified: true,
      changeType: 'positive'
    },
    {
      rank: 10,
      name: 'Cool Cats NFT',
      image: 'https://i.seadn.io/gae/LIov33kogXOK4XZd2ESj29sqm_Hww5JSdO7AFn5wjt8xgnJJ0UpNV9yITqxra3s_LMEW1AnnrgOVB_hDpjJRA1uF4skI5Sdi_9rULi8?auto=format&dpr=1&w=384',
      volume: '2.14',
      change: '-5.2%',
      verified: true,
      changeType: 'negative'
    },
    {
      rank: 11,
      name: 'World of Women',
      image: 'https://i2.seadn.io/ethereum/5e4e4040a89f41b4a6dbd7a05ea2fc42/04de2d9aaec98dd389e3af1b1a14b6/8604de2d9aaec98dd389e3af1b1a14b6.gif?w=100&h=100&fit=crop',
      volume: '1.78',
      change: '+9.3%',
      verified: true,
      changeType: 'positive'
    },
    {
      rank: 12,
      name: 'VeeFriends',
      image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=100&h=100&fit=crop&crop=center',
      volume: '1.56',
      change: '+3.7%',
      verified: true,
      changeType: 'positive'
    },
    {
      rank: 13,
      name: 'Moonbirds',
      image: 'https://lh3.googleusercontent.com/H-eyNE1MwL5ohL-tCfn_Xa1Sl9M9B4612tLYeUlQubzt4ewhr4huJIR5OLuyO3Z5PpJFSwdm7rq-TikAh7f5eUw338A2cy6HRH75?w=500',
      volume: '2.67',
      change: '-8.1%',
      verified: true,
      changeType: 'negative'
    },
    {
      rank: 14,
      name: 'Otherdeeds for Otherside',
      image: 'https://images.unsplash.com/photo-1550745165-9bc0b252726f?w=100&h=100&fit=crop&crop=center',
      volume: '0.89',
      change: '+22.5%',
      verified: true,
      changeType: 'positive'
    },
    {
      rank: 15,
      name: 'Lil Pudgys',
      image: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=100&h=100&fit=crop&crop=center',
      volume: '1.23',
      change: '+11.8%',
      verified: true,
      changeType: 'positive'
    }
  ];

  const notableDrops = [
    {
      title: 'Captain Apeshit #8817',
      artist: 'Yuga Labs',
      image: '/Captain_Apeshit.png',
      description: 'Iconic digital collectible from the world-famous Bored Ape Yacht Club'
    },
    {
      title: 'Pixel Fever #7804',
      artist: 'Larva Labs',
      image: '/Pixel_Fever.png',
      description: 'One of the original 10,000 algorithmically generated pixel art characters'
    },
    {
      title: 'Suzi #9605',
      artist: 'Chiru Labs',
      image: '/Suzi.png',
      description: 'Anime-inspired NFT with unique traits and metaverse utility'
    }
  ];

  const categories = [
    {
      name: 'Art',
      image: 'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=300&h=200&fit=crop',
      description: 'Digital masterpieces and generative art'
    },
    {
      name: 'Collectibles',
      image: 'https://images.unsplash.com/photo-1621761191319-c6fb62004040?w=300&h=200&fit=crop',
      description: 'Unique digital collectibles and trading cards'
    },
    {
      name: 'Gaming',
      image: 'https://images.unsplash.com/photo-1511512578047-dfb367046420?w=300&h=200&fit=crop',
      description: 'In-game assets and virtual worlds'
    },
    {
      name: 'Music',
      image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=300&h=200&fit=crop',
      description: 'Audio NFTs and music collectibles'
    }
  ];

  return (
    <div className="nft-trade-page">
      <Navbar />

      {/* Hero Section */}
      <section className="nft-hero">
        <div className="nft-hero-container">
          <div className="nft-hero-content">
            <div className="nft-breadcrumb">
              <a href="/">Home</a>
              <span>/</span>
              <span>NFT Trade</span>
            </div>
            <h1 className="nft-hero-title">
              Discover, Collect & Trade <span className="highlight">Premium NFTs</span>
            </h1>
            <p className="nft-hero-description">
              The most secure and professional platform for NFT trading. Connect your wallet
              and explore the world's most valuable digital assets.
            </p>
            <div className="nft-hero-buttons">
              <button className="nft-connect-wallet">
                <span className="wallet-icon">👛</span>
                Connect Wallet
              </button>
              <button className="nft-explore">Explore Collections</button>
            </div>
          </div>
          <div className="nft-hero-image">
            <img src="https://images.unsplash.com/photo-1639762681485-074b7f938ba0?w=600&h=400&fit=crop" alt="NFT Trading" />
          </div>
        </div>
      </section>

      {/* Notable Drops Section */}
      <section className="nft-notable-drops">
        <div className="nft-notable-container">
          <h2 className="nft-section-title">Notable Drops</h2>
          <div className="nft-drops-grid">
            {notableDrops.map((drop, index) => (
              <div key={index} className="nft-drop-card">
                <div className="nft-drop-image">
                  <img src={drop.image} alt={drop.title} />
                </div>
                <div className="nft-drop-content">
                  <h3 className="nft-drop-title">{drop.title}</h3>
                  <p className="nft-drop-artist">by {drop.artist}</p>
                  <p className="nft-drop-description">{drop.description}</p>
                  <button className="nft-drop-button">View Collection</button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Top Collections Section */}
      <section className="nft-top-collections">
        <div className="nft-collections-container">
          <div className="nft-collections-header">
            <h2 className="nft-section-title">Top Collections</h2>
            <p className="nft-section-subtitle">Trending NFT collections by volume and activity</p>
          </div>
          <div className="nft-collections-grid">
            {topCollections.map((collection, index) => (
              <div key={index} className="nft-collection-card">
                <div className="nft-collection-rank">{collection.rank}</div>
                <div className="nft-collection-image">
                  <img src={collection.image} alt={collection.name} />
                  {collection.verified && <div className="nft-verified">✓</div>}
                </div>
                <div className="nft-collection-info">
                  <h3 className="nft-collection-name">{collection.name}</h3>
                  <div className="nft-collection-stats">
                    <div className="nft-volume">
                      <span className="eth-icon">Ξ</span>
                      <span className="volume-amount">{collection.volume}</span>
                    </div>
                    <div className={`nft-change ${collection.changeType}`}>
                      {collection.change}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className="nft-view-all">
            <button className="nft-view-all-button">View All Rankings →</button>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="nft-how-it-works">
        <div className="nft-how-container">
          <h2 className="nft-section-title">Discover and Collect NFTs</h2>
          <div className="nft-steps-grid">
            <div className="nft-step">
              <div className="nft-step-icon">👛</div>
              <h3 className="nft-step-title">Set up your wallet</h3>
              <p className="nft-step-description">
                Connect your preferred crypto wallet to BullSeed NFT marketplace.
                We support MetaMask, WalletConnect, and more.
              </p>
            </div>
            <div className="nft-step">
              <div className="nft-step-icon">🔍</div>
              <h3 className="nft-step-title">Explore collections</h3>
              <p className="nft-step-description">
                Browse through thousands of verified NFT collections across
                art, gaming, music, and collectibles categories.
              </p>
            </div>
            <div className="nft-step">
              <div className="nft-step-icon">📤</div>
              <h3 className="nft-step-title">Create & mint NFTs</h3>
              <p className="nft-step-description">
                Upload your digital artwork, add metadata, and mint your
                NFTs with customizable properties and unlockable content.
              </p>
            </div>
            <div className="nft-step">
              <div className="nft-step-icon">💰</div>
              <h3 className="nft-step-title">Trade securely</h3>
              <p className="nft-step-description">
                List your NFTs for auction or fixed price. Our secure smart
                contracts ensure safe and transparent transactions.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="nft-categories">
        <div className="nft-categories-container">
          <h2 className="nft-section-title">Browse by Categories</h2>
          <div className="nft-categories-grid">
            {categories.map((category, index) => (
              <div key={index} className="nft-category-card">
                <div className="nft-category-image">
                  <img src={category.image} alt={category.name} />
                </div>
                <div className="nft-category-content">
                  <h3 className="nft-category-name">{category.name}</h3>
                  <p className="nft-category-description">{category.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default NFTTrade;