/* Deposit Page Styles */
.deposit {
  padding: 32px;
  max-width: 1200px;
  margin: 0 auto;
}

.deposit-header {
  margin-bottom: 32px;
}

.deposit-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin-bottom: 8px;
}

.deposit-header p {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
}

.deposit-content {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 32px;
}

.deposit-main {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.deposit-crypto-selector {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
  backdrop-filter: blur(10px);
}

.deposit-crypto-selector h3 {
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin-bottom: 20px;
}

.crypto-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.crypto-option {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  width: 100%;
}

.crypto-option:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

.crypto-option.active {
  background: rgba(0, 212, 170, 0.1);
  border-color: #00d4aa;
}

.crypto-option-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 700;
}

.crypto-option-info {
  flex: 1;
}

.crypto-option-name {
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 4px;
}

.crypto-option-symbol {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
}

.crypto-option-network {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
}

.deposit-details {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
  backdrop-filter: blur(10px);
}

.deposit-details-header {
  margin-bottom: 24px;
}

.deposit-crypto-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.deposit-crypto-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 700;
}

.deposit-crypto-info h3 {
  font-size: 20px;
  font-weight: 600;
  color: white;
  margin-bottom: 4px;
}

.deposit-crypto-info p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
}

.deposit-address-section {
  margin-bottom: 24px;
}

.deposit-address-section h4 {
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 12px;
}

.deposit-address-container {
  margin-bottom: 20px;
}

.deposit-address {
  display: flex;
  gap: 8px;
}

.deposit-address-input {
  flex: 1;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-family: 'Courier New', monospace;
}

.deposit-copy-btn {
  background: rgba(0, 212, 170, 0.1);
  border: 1px solid rgba(0, 212, 170, 0.3);
  color: #00d4aa;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.deposit-copy-btn:hover {
  background: rgba(0, 212, 170, 0.15);
  border-color: rgba(0, 212, 170, 0.5);
}

.deposit-qr-code {
  text-align: center;
}

.qr-code-image {
  width: 200px;
  height: 200px;
  border-radius: 8px;
  margin-bottom: 12px;
}

.deposit-qr-code p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
}

.deposit-info {
  margin-bottom: 24px;
}

.deposit-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.deposit-info-item:last-child {
  border-bottom: none;
}

.deposit-info-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.deposit-info-value {
  font-size: 14px;
  color: white;
  font-weight: 600;
}

.deposit-warnings {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.deposit-warning {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.3);
  border-radius: 8px;
  color: #f59e0b;
  font-size: 14px;
  line-height: 1.5;
}

.deposit-warning svg {
  flex-shrink: 0;
  margin-top: 2px;
}

.deposit-sidebar {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.deposit-history {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
  backdrop-filter: blur(10px);
}

.deposit-history h3 {
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin-bottom: 20px;
}

.deposit-history-empty {
  text-align: center;
  padding: 40px 20px;
  color: rgba(255, 255, 255, 0.6);
}

.deposit-history-empty svg {
  margin-bottom: 16px;
  opacity: 0.5;
}

.deposit-history-empty p {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}

.deposit-history-empty span {
  font-size: 14px;
  opacity: 0.8;
}

.deposit-support {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
  backdrop-filter: blur(10px);
}

.deposit-support h3 {
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin-bottom: 12px;
}

.deposit-support p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 20px;
  line-height: 1.5;
}

.deposit-support-btn {
  background: rgba(0, 212, 170, 0.1);
  border: 1px solid rgba(0, 212, 170, 0.3);
  color: #00d4aa;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  justify-content: center;
}

.deposit-support-btn:hover {
  background: rgba(0, 212, 170, 0.15);
  border-color: rgba(0, 212, 170, 0.5);
}

/* Mobile Responsive */
@media (max-width: 1024px) {
  .deposit-content {
    grid-template-columns: 1fr;
    gap: 24px;
  }
}

@media (max-width: 768px) {
  .deposit {
    padding: 16px;
  }
  
  .deposit-header h1 {
    font-size: 24px;
  }
  
  .deposit-crypto-selector,
  .deposit-details,
  .deposit-history,
  .deposit-support {
    padding: 16px;
  }
  
  .crypto-option {
    padding: 12px;
  }
  
  .crypto-option-icon {
    width: 32px;
    height: 32px;
    font-size: 16px;
  }
  
  .deposit-crypto-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }
  
  .qr-code-image {
    width: 150px;
    height: 150px;
  }
}
