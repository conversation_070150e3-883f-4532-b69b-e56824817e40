import React, { useState, useEffect } from 'react';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import '../styles/Markets.css';

const Markets = () => {
  const [marketData, setMarketData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [sortBy, setSortBy] = useState('market_cap_desc');
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch real crypto market data with retry logic
  useEffect(() => {
    const fetchMarketData = async (retryCount = 0) => {
      try {
        setLoading(true);
        setError(null);

        // Map our sort options to CoinGecko API parameters
        let apiSortParam = 'market_cap_desc';
        switch (sortBy) {
          case 'market_cap_desc':
            apiSortParam = 'market_cap_desc';
            break;
          case 'market_cap_asc':
            apiSortParam = 'market_cap_asc';
            break;
          case 'price_desc':
            apiSortParam = 'price_desc';
            break;
          case 'price_asc':
            apiSortParam = 'price_asc';
            break;
          case 'volume_desc':
            apiSortParam = 'volume_desc';
            break;
          case 'id_asc':
            apiSortParam = 'id_asc';
            break;
          default:
            apiSortParam = 'market_cap_desc';
        }

        // Try multiple API endpoints for better reliability
        const apiUrls = [
          `https://api.coingecko.com/api/v3/coins/markets?vs_currency=usd&order=${apiSortParam}&per_page=100&page=1&sparkline=true&price_change_percentage=1h,24h,7d`,
          `https://api.coingecko.com/api/v3/coins/markets?vs_currency=usd&order=${apiSortParam}&per_page=50&page=1&sparkline=false&price_change_percentage=24h,7d`
        ];

        let response;
        let data;

        for (let i = 0; i < apiUrls.length; i++) {
          try {
            console.log(`Trying API URL ${i + 1}:`, apiUrls[i]);
            response = await fetch(apiUrls[i], {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
              },
            });

            if (response.ok) {
              data = await response.json();
              if (Array.isArray(data) && data.length > 0) {
                console.log(`Successfully fetched ${data.length} coins with sort: ${apiSortParam}`);
                setMarketData(data);
                setError(null);
                return; // Success, exit function
              }
            } else {
              console.log(`API ${i + 1} failed with status:`, response.status);
            }
          } catch (apiError) {
            console.log(`API ${i + 1} error:`, apiError.message);
            continue; // Try next API
          }
        }

        // If all APIs failed, throw error
        throw new Error(`All API endpoints failed. Status: ${response?.status || 'Network Error'}`);

      } catch (error) {
        console.error('Error fetching market data:', error);

        // Retry logic - try up to 3 times with exponential backoff
        if (retryCount < 3) {
          console.log(`Retrying... Attempt ${retryCount + 1}/3`);
          setTimeout(() => {
            fetchMarketData(retryCount + 1);
          }, Math.pow(2, retryCount) * 1000); // 1s, 2s, 4s delays
          return;
        }

        // After all retries failed
        setError(`Failed to load market data after ${retryCount + 1} attempts. This might be due to API rate limiting. Please try again in a few minutes.`);
        setMarketData([]); // Set empty array to prevent black screen
      } finally {
        setLoading(false);
      }
    };

    fetchMarketData();

    // Update data every 2 minutes (less frequent to avoid rate limiting)
    const interval = setInterval(() => fetchMarketData(), 120000);

    return () => clearInterval(interval);
  }, [sortBy]);

  // Filter data based on search term
  const filteredData = marketData.filter(coin =>
    coin.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    coin.symbol.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const formatPrice = (price) => {
    if (price < 0.01) {
      return `$${price.toFixed(8)}`;
    } else if (price < 1) {
      return `$${price.toFixed(6)}`;
    } else if (price < 100) {
      return `$${price.toFixed(4)}`;
    } else {
      return `$${price.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    }
  };

  const formatMarketCap = (marketCap) => {
    if (marketCap >= 1e12) {
      return `$${(marketCap / 1e12).toFixed(2)}T`;
    } else if (marketCap >= 1e9) {
      return `$${(marketCap / 1e9).toFixed(2)}B`;
    } else if (marketCap >= 1e6) {
      return `$${(marketCap / 1e6).toFixed(2)}M`;
    } else {
      return `$${marketCap.toLocaleString()}`;
    }
  };

  const formatVolume = (volume) => {
    if (volume >= 1e9) {
      return `$${(volume / 1e9).toFixed(2)}B`;
    } else if (volume >= 1e6) {
      return `$${(volume / 1e6).toFixed(2)}M`;
    } else {
      return `$${volume.toLocaleString()}`;
    }
  };

  const formatChange = (change) => {
    if (change === null || change === undefined) return 'N/A';
    const sign = change >= 0 ? '+' : '';
    return `${sign}${change.toFixed(2)}%`;
  };

  const getChangeClass = (change) => {
    if (change === null || change === undefined) return 'neutral';
    return change >= 0 ? 'positive' : 'negative';
  };

  return (
    <div className="markets-page">
      <Navbar />
      
      {/* Hero Section */}
      <section className="markets-hero">
        <div className="markets-hero-container">
          <div className="markets-breadcrumb">
            <a href="/">Home</a>
            <span>/</span>
            <span>Markets</span>
          </div>
          
          <div className="markets-hero-content">
            <div className="markets-hero-badge">
              <span className="markets-hero-badge-icon">📈</span>
              Live Market Data
            </div>
            <h1 className="markets-hero-title">
              Cryptocurrency <span className="highlight">Markets</span>
            </h1>
            <p className="markets-hero-description">
              Real-time cryptocurrency prices, market capitalizations, and trading volumes. 
              Stay informed with live market data to make better investment decisions.
            </p>
          </div>
        </div>
      </section>

      {/* Market Controls */}
      <section className="markets-controls">
        <div className="markets-controls-container">
          <div className="markets-search">
            <div className="search-input-wrapper">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="11" cy="11" r="8"/>
                <path d="m21 21-4.35-4.35"/>
              </svg>
              <input
                type="text"
                placeholder="Search cryptocurrencies..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input"
              />
            </div>
          </div>
          
          <div className="markets-sort">
            <select
              value={sortBy}
              onChange={(e) => {
                console.log('Sort changed to:', e.target.value);
                setSortBy(e.target.value);
              }}
              className="sort-select"
            >
              <option value="market_cap_desc">Market Cap ↓ (High to Low)</option>
              <option value="market_cap_asc">Market Cap ↑ (Low to High)</option>
              <option value="price_desc">Price ↓ (High to Low)</option>
              <option value="price_asc">Price ↑ (Low to High)</option>
              <option value="volume_desc">Volume ↓ (High to Low)</option>
              <option value="id_asc">Name ↑ (A to Z)</option>
            </select>
          </div>
        </div>
      </section>

      {/* Market Data Table */}
      <section className="markets-data">
        <div className="markets-data-container">
          {loading ? (
            <div className="markets-loading">
              <div className="loading-spinner"></div>
              <p>Loading market data...</p>
            </div>
          ) : error ? (
            <div className="markets-error">
              <div className="error-icon">⚠️</div>
              <p>{error}</p>
              <div className="error-actions">
                <button
                  onClick={() => {
                    setError(null);
                    setLoading(true);
                    // Trigger a fresh fetch by changing the sortBy state
                    setSortBy(prev => prev);
                  }}
                  className="retry-button"
                >
                  🔄 Try Again
                </button>
                <button
                  onClick={() => window.location.reload()}
                  className="refresh-button"
                >
                  🔃 Refresh Page
                </button>
              </div>
            </div>
          ) : filteredData.length === 0 ? (
            <div className="markets-no-results">
              <div className="no-results-icon">🔍</div>
              <h3>No cryptocurrencies found</h3>
              <p>Try adjusting your search term or filters to find what you're looking for.</p>
            </div>
          ) : (
            <div className="markets-table-wrapper">
              <table className="markets-table">
                <thead>
                  <tr>
                    <th className="rank-col">#</th>
                    <th className="name-col">Name</th>
                    <th className="price-col">Price</th>
                    <th className="change-col">1h %</th>
                    <th className="change-col">24h %</th>
                    <th className="change-col">7d %</th>
                    <th className="volume-col">24h Volume</th>
                    <th className="marketcap-col">Market Cap</th>
                    <th className="chart-col">Last 7 Days</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredData.map((coin, index) => (
                    <tr key={coin.id} className="market-row">
                      <td className="rank-cell">{coin.market_cap_rank || index + 1}</td>
                      <td className="name-cell">
                        <div className="coin-info">
                          <img 
                            src={coin.image} 
                            alt={coin.name}
                            className="coin-logo"
                            onError={(e) => {
                              e.target.style.display = 'none';
                            }}
                          />
                          <div className="coin-details">
                            <span className="coin-name">{coin.name}</span>
                            <span className="coin-symbol">{coin.symbol.toUpperCase()}</span>
                          </div>
                        </div>
                      </td>
                      <td className="price-cell">{formatPrice(coin.current_price)}</td>
                      <td className={`change-cell ${getChangeClass(coin.price_change_percentage_1h_in_currency)}`}>
                        {formatChange(coin.price_change_percentage_1h_in_currency)}
                      </td>
                      <td className={`change-cell ${getChangeClass(coin.price_change_percentage_24h)}`}>
                        {formatChange(coin.price_change_percentage_24h)}
                      </td>
                      <td className={`change-cell ${getChangeClass(coin.price_change_percentage_7d_in_currency)}`}>
                        {formatChange(coin.price_change_percentage_7d_in_currency)}
                      </td>
                      <td className="volume-cell">{formatVolume(coin.total_volume)}</td>
                      <td className="marketcap-cell">{formatMarketCap(coin.market_cap)}</td>
                      <td className="chart-cell">
                        {coin.sparkline_in_7d && coin.sparkline_in_7d.price && (
                          <div className="mini-chart">
                            <svg width="100" height="40" viewBox="0 0 100 40">
                              <polyline
                                fill="none"
                                stroke={coin.price_change_percentage_7d_in_currency >= 0 ? "#00d4aa" : "#ef4444"}
                                strokeWidth="2"
                                points={coin.sparkline_in_7d.price
                                  .map((price, i) => `${(i / (coin.sparkline_in_7d.price.length - 1)) * 100},${40 - ((price - Math.min(...coin.sparkline_in_7d.price)) / (Math.max(...coin.sparkline_in_7d.price) - Math.min(...coin.sparkline_in_7d.price))) * 40}`)
                                  .join(' ')}
                              />
                            </svg>
                          </div>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Markets;
