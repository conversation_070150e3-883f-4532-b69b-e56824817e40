import React, { useState } from 'react';
import '../styles/AccountSettings.css';

const AccountSettings = ({ user }) => {
  const [activeTab, setActiveTab] = useState('security');
  const [settings, setSettings] = useState({
    twoFactorEnabled: false,
    emailNotifications: true,
    smsNotifications: false,
    marketingEmails: true,
    loginAlerts: true
  });

  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const handleSettingChange = (setting, value) => {
    setSettings(prev => ({
      ...prev,
      [setting]: value
    }));
  };

  const handlePasswordChange = (field, value) => {
    setPasswordForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePasswordSubmit = (e) => {
    e.preventDefault();
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      alert('Passwords do not match');
      return;
    }
    // Handle password change
    console.log('Password change requested');
    setPasswordForm({ currentPassword: '', newPassword: '', confirmPassword: '' });
  };

  const handleEnable2FA = () => {
    // Handle 2FA setup
    console.log('2FA setup requested');
    setSettings(prev => ({ ...prev, twoFactorEnabled: true }));
  };

  const renderSecurityTab = () => (
    <div className="settings-tab-content">
      <div className="settings-section">
        <h3>Password</h3>
        <p>Update your password to keep your account secure</p>
        <form onSubmit={handlePasswordSubmit} className="password-form">
          <div className="settings-form-group">
            <label>Current Password</label>
            <input
              type="password"
              value={passwordForm.currentPassword}
              onChange={(e) => handlePasswordChange('currentPassword', e.target.value)}
              placeholder="Enter current password"
              required
            />
          </div>
          <div className="settings-form-group">
            <label>New Password</label>
            <input
              type="password"
              value={passwordForm.newPassword}
              onChange={(e) => handlePasswordChange('newPassword', e.target.value)}
              placeholder="Enter new password"
              required
            />
          </div>
          <div className="settings-form-group">
            <label>Confirm New Password</label>
            <input
              type="password"
              value={passwordForm.confirmPassword}
              onChange={(e) => handlePasswordChange('confirmPassword', e.target.value)}
              placeholder="Confirm new password"
              required
            />
          </div>
          <button type="submit" className="settings-btn primary">
            Update Password
          </button>
        </form>
      </div>

      <div className="settings-section">
        <h3>Two-Factor Authentication</h3>
        <p>Add an extra layer of security to your account</p>
        <div className="settings-2fa">
          <div className="settings-2fa-status">
            <div className="settings-2fa-icon">
              {settings.twoFactorEnabled ? (
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                  <path d="M9 12l2 2 4-4"/>
                </svg>
              ) : (
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                  <line x1="9" y1="9" x2="15" y2="15"/>
                  <line x1="15" y1="9" x2="9" y2="15"/>
                </svg>
              )}
            </div>
            <div className="settings-2fa-info">
              <h4>{settings.twoFactorEnabled ? 'Enabled' : 'Disabled'}</h4>
              <p>
                {settings.twoFactorEnabled 
                  ? 'Your account is protected with 2FA'
                  : 'Secure your account with 2FA'
                }
              </p>
            </div>
          </div>
          <button 
            className={`settings-btn ${settings.twoFactorEnabled ? 'secondary' : 'primary'}`}
            onClick={handleEnable2FA}
          >
            {settings.twoFactorEnabled ? 'Manage 2FA' : 'Enable 2FA'}
          </button>
        </div>
      </div>

      <div className="settings-section">
        <h3>Login Sessions</h3>
        <p>Manage your active login sessions</p>
        <div className="settings-sessions">
          <div className="settings-session">
            <div className="settings-session-info">
              <h4>Current Session</h4>
              <p>Windows • Chrome • New York, NY</p>
              <span className="settings-session-time">Active now</span>
            </div>
            <span className="settings-session-current">Current</span>
          </div>
          <div className="settings-session">
            <div className="settings-session-info">
              <h4>Mobile Session</h4>
              <p>iOS • Safari • New York, NY</p>
              <span className="settings-session-time">2 hours ago</span>
            </div>
            <button className="settings-session-revoke">Revoke</button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderNotificationsTab = () => (
    <div className="settings-tab-content">
      <div className="settings-section">
        <h3>Email Notifications</h3>
        <p>Choose what email notifications you'd like to receive</p>
        <div className="settings-toggles">
          <div className="settings-toggle">
            <div className="settings-toggle-info">
              <h4>Account Activity</h4>
              <p>Login alerts, password changes, and security updates</p>
            </div>
            <label className="settings-switch">
              <input
                type="checkbox"
                checked={settings.loginAlerts}
                onChange={(e) => handleSettingChange('loginAlerts', e.target.checked)}
              />
              <span className="settings-slider"></span>
            </label>
          </div>

          <div className="settings-toggle">
            <div className="settings-toggle-info">
              <h4>Investment Updates</h4>
              <p>Daily returns, investment completions, and portfolio updates</p>
            </div>
            <label className="settings-switch">
              <input
                type="checkbox"
                checked={settings.emailNotifications}
                onChange={(e) => handleSettingChange('emailNotifications', e.target.checked)}
              />
              <span className="settings-slider"></span>
            </label>
          </div>

          <div className="settings-toggle">
            <div className="settings-toggle-info">
              <h4>Marketing Communications</h4>
              <p>Product updates, new features, and promotional offers</p>
            </div>
            <label className="settings-switch">
              <input
                type="checkbox"
                checked={settings.marketingEmails}
                onChange={(e) => handleSettingChange('marketingEmails', e.target.checked)}
              />
              <span className="settings-slider"></span>
            </label>
          </div>
        </div>
      </div>

      <div className="settings-section">
        <h3>SMS Notifications</h3>
        <p>Receive important alerts via text message</p>
        <div className="settings-toggles">
          <div className="settings-toggle">
            <div className="settings-toggle-info">
              <h4>Security Alerts</h4>
              <p>Login attempts and security-related notifications</p>
            </div>
            <label className="settings-switch">
              <input
                type="checkbox"
                checked={settings.smsNotifications}
                onChange={(e) => handleSettingChange('smsNotifications', e.target.checked)}
              />
              <span className="settings-slider"></span>
            </label>
          </div>
        </div>
      </div>
    </div>
  );

  const renderPreferencesTab = () => (
    <div className="settings-tab-content">
      <div className="settings-section">
        <h3>Display Preferences</h3>
        <div className="settings-form-group">
          <label>Language</label>
          <select defaultValue="en">
            <option value="en">English</option>
            <option value="es">Spanish</option>
            <option value="fr">French</option>
            <option value="de">German</option>
            <option value="ja">Japanese</option>
          </select>
        </div>
        <div className="settings-form-group">
          <label>Timezone</label>
          <select defaultValue="America/New_York">
            <option value="America/New_York">Eastern Time (ET)</option>
            <option value="America/Chicago">Central Time (CT)</option>
            <option value="America/Denver">Mountain Time (MT)</option>
            <option value="America/Los_Angeles">Pacific Time (PT)</option>
            <option value="UTC">UTC</option>
          </select>
        </div>
        <div className="settings-form-group">
          <label>Currency Display</label>
          <select defaultValue="USD">
            <option value="USD">USD ($)</option>
            <option value="EUR">EUR (€)</option>
            <option value="GBP">GBP (£)</option>
            <option value="JPY">JPY (¥)</option>
          </select>
        </div>
      </div>

      <div className="settings-section">
        <h3>Account Actions</h3>
        <div className="settings-actions">
          <button className="settings-btn secondary">
            Export Account Data
          </button>
          <button className="settings-btn danger">
            Delete Account
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="account-settings">
      <div className="account-settings-header">
        <h1>Account Settings</h1>
        <p>Manage your security, notifications, and preferences</p>
      </div>

      <div className="account-settings-content">
        <div className="settings-tabs">
          <button 
            className={`settings-tab ${activeTab === 'security' ? 'active' : ''}`}
            onClick={() => setActiveTab('security')}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
            </svg>
            Security
          </button>
          <button 
            className={`settings-tab ${activeTab === 'notifications' ? 'active' : ''}`}
            onClick={() => setActiveTab('notifications')}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"/>
              <path d="M13.73 21a2 2 0 0 1-3.46 0"/>
            </svg>
            Notifications
          </button>
          <button 
            className={`settings-tab ${activeTab === 'preferences' ? 'active' : ''}`}
            onClick={() => setActiveTab('preferences')}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <circle cx="12" cy="12" r="3"/>
              <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
            </svg>
            Preferences
          </button>
        </div>

        <div className="settings-content">
          {activeTab === 'security' && renderSecurityTab()}
          {activeTab === 'notifications' && renderNotificationsTab()}
          {activeTab === 'preferences' && renderPreferencesTab()}
        </div>
      </div>
    </div>
  );
};

export default AccountSettings;
