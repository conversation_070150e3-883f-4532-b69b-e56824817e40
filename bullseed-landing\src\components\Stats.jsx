import React, { useState, useEffect, useRef } from 'react';

const Stats = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [counts, setCounts] = useState({
    users: 0,
    countries: 0,
    withdrawn: 0,
    investors: 0
  });
  
  const statsRef = useRef(null);

  const statsData = [
    {
      key: 'users',
      value: 36000000,
      suffix: 'M',
      displayValue: '36M',
      label: 'Registered users',
      icon: '👥'
    },
    {
      key: 'countries',
      value: 178,
      suffix: '',
      displayValue: '178',
      label: 'Countries supported',
      icon: '🌍'
    },
    {
      key: 'withdrawn',
      value: 10000000,
      suffix: 'M',
      displayValue: '$10M',
      label: 'Withdrawn each month',
      icon: '💰'
    },
    {
      key: 'investors',
      value: 18000,
      suffix: 'k',
      displayValue: '18k',
      label: 'Active investors daily',
      icon: '📈'
    }
  ];

  // Intersection Observer to trigger animation when component comes into view
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true);
        }
      },
      {
        threshold: 0.3, // Trigger when 30% of component is visible
        rootMargin: '0px 0px -50px 0px' // Trigger slightly before fully visible
      }
    );

    if (statsRef.current) {
      observer.observe(statsRef.current);
    }

    return () => {
      if (statsRef.current) {
        observer.unobserve(statsRef.current);
      }
    };
  }, [isVisible]);

  // Count up animation
  useEffect(() => {
    if (!isVisible) return;

    const duration = 2000; // 2 seconds
    const steps = 60; // 60 steps for smooth animation
    const stepDuration = duration / steps;

    statsData.forEach((stat) => {
      let currentStep = 0;
      const increment = stat.value / steps;

      const timer = setInterval(() => {
        currentStep++;
        const currentValue = Math.min(increment * currentStep, stat.value);
        
        setCounts(prev => ({
          ...prev,
          [stat.key]: Math.floor(currentValue)
        }));

        if (currentStep >= steps) {
          clearInterval(timer);
        }
      }, stepDuration);
    });
  }, [isVisible]);

  // Format number for display
  const formatNumber = (num, suffix, originalValue) => {
    if (suffix === 'M') {
      return `${(num / 1000000).toFixed(num >= originalValue ? 0 : 1)}M`;
    } else if (suffix === 'k') {
      return `${(num / 1000).toFixed(num >= originalValue ? 0 : 1)}k`;
    } else if (originalValue === 10000000) {
      // Special case for $10M
      return `$${(num / 1000000).toFixed(num >= originalValue ? 0 : 1)}M`;
    }
    return num.toString();
  };

  return (
    <section className="stats" ref={statsRef}>
      <div className="stats-container">
        <div className="stats-grid">
          {statsData.map((stat, index) => (
            <div key={stat.key} className="stat-card" style={{ animationDelay: `${index * 0.1}s` }}>
              <div className="stat-icon">
                {stat.icon}
              </div>
              <div className="stat-content">
                <div className="stat-number">
                  {formatNumber(counts[stat.key], stat.suffix, stat.value)}
                </div>
                <div className="stat-label">
                  {stat.label}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Stats;
