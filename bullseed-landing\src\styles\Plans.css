/* Plans Page Styles */
.plans-page {
  min-height: 100vh;
  background-color: #0a0a0a;
  color: #ffffff;
}

/* Hero Section */
.plans-hero {
  padding: 8rem 0 4rem;
  background: linear-gradient(135deg, #0a0a0a 0%, #111111 100%);
  position: relative;
  overflow: hidden;
}

.plans-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 30%, rgba(0, 212, 170, 0.08) 0%, transparent 50%),
              radial-gradient(circle at 80% 70%, rgba(79, 70, 229, 0.08) 0%, transparent 50%);
  pointer-events: none;
}

.plans-hero-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.plans-breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 2rem;
  font-size: 0.9rem;
  color: #a0a0a0;
}

.plans-breadcrumb a {
  color: #00d4aa;
  text-decoration: none;
  transition: color 0.3s ease;
}

.plans-breadcrumb a:hover {
  color: #00b894;
}

.plans-breadcrumb span {
  color: #666;
}

.plans-hero-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.plans-hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.875rem;
  color: #a0a0a0;
  margin-bottom: 2rem;
}

.plans-hero-badge-icon {
  font-size: 1rem;
}

.plans-hero-title {
  font-size: 4rem;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  color: #ffffff;
}

.plans-hero-title .highlight {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.plans-hero-description {
  font-size: 1.25rem;
  color: #a0a0a0;
  line-height: 1.6;
  margin-bottom: 2rem;
}

/* Deposit Portfolio Section */
.deposit-portfolio {
  padding: 4rem 0;
  background: linear-gradient(180deg, #111111 0%, #0a0a0a 100%);
}

.deposit-portfolio-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.deposit-portfolio-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.deposit-portfolio-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: #ffffff;
}

.deposit-portfolio-description {
  font-size: 1.125rem;
  color: #a0a0a0;
  line-height: 1.6;
}

/* Plans Overview Section */
.plans-overview {
  padding: 6rem 0;
  background: linear-gradient(180deg, #0a0a0a 0%, #111111 100%);
  position: relative;
}

.plans-overview::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 70% 20%, rgba(0, 212, 170, 0.05) 0%, transparent 50%),
              radial-gradient(circle at 30% 80%, rgba(79, 70, 229, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.plans-overview-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.plans-overview-title {
  font-size: 3rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 4rem;
  color: #ffffff;
}

.plans-overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.plan-overview-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.plan-overview-card:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(0, 212, 170, 0.3);
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 212, 170, 0.1);
}

.plan-overview-icon {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin: 0 auto 1.5rem;
  transition: transform 0.3s ease;
}

.plan-overview-card:hover .plan-overview-icon {
  transform: scale(1.1);
}

.plan-overview-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 2rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.plan-overview-stats {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

.plan-overview-stat {
  flex: 1;
  text-align: center;
}

.plan-overview-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  font-family: 'Monaco', 'Menlo', monospace;
}

.plan-overview-label {
  font-size: 0.875rem;
  color: #a0a0a0;
  font-weight: 500;
}

.plan-overview-arrow {
  color: #00d4aa;
  opacity: 0.7;
}

/* Referral Bonus Section */
.referral-bonus {
  padding: 6rem 0;
  background: linear-gradient(135deg, #111111 0%, #0a0a0a 100%);
}

.referral-bonus-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.referral-bonus-title {
  font-size: 3rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 4rem;
  color: #ffffff;
}

.referral-bonus-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.referral-bonus-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.referral-bonus-card:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(0, 212, 170, 0.3);
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 212, 170, 0.1);
}

.referral-bonus-icon {
  color: #00d4aa;
  margin-bottom: 1rem;
}

.referral-bonus-percentage {
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #00d4aa, #00b894);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
  font-family: 'Monaco', 'Menlo', monospace;
}

.referral-bonus-level {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.5rem;
}

.referral-bonus-description {
  font-size: 0.875rem;
  color: #a0a0a0;
}

/* Detailed Plans Section */
.detailed-plans {
  padding: 6rem 0;
  background: linear-gradient(180deg, #0a0a0a 0%, #111111 100%);
  position: relative;
}

.detailed-plans::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 40% 30%, rgba(0, 212, 170, 0.05) 0%, transparent 50%),
              radial-gradient(circle at 60% 70%, rgba(79, 70, 229, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.detailed-plans-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.detailed-plans-title {
  font-size: 3rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 4rem;
  color: #ffffff;
}

.detailed-plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.detailed-plan-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 24px;
  padding: 2.5rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  position: relative;
}

.detailed-plan-card:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(0, 212, 170, 0.3);
  transform: translateY(-8px);
  box-shadow: 0 25px 50px rgba(0, 212, 170, 0.15);
}

.detailed-plan-card.popular {
  border-color: rgba(0, 212, 170, 0.5);
  background: rgba(0, 212, 170, 0.03);
}

.detailed-plan-popular-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #00d4aa, #00b894);
  color: #ffffff;
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detailed-plan-header {
  text-align: center;
  margin-bottom: 2rem;
}

.detailed-plan-icon {
  width: 80px;
  height: 80px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  margin: 0 auto 1.5rem;
  transition: transform 0.3s ease;
}

.detailed-plan-card:hover .detailed-plan-icon {
  transform: scale(1.1);
}

.detailed-plan-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detailed-plan-description {
  font-size: 1rem;
  color: #a0a0a0;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.detailed-plan-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.detailed-plan-stat {
  text-align: center;
}

.detailed-plan-stat-value {
  display: block;
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  font-family: 'Monaco', 'Menlo', monospace;
}

.detailed-plan-stat-label {
  font-size: 0.875rem;
  color: #a0a0a0;
  font-weight: 500;
}

.detailed-plan-features {
  margin-bottom: 2rem;
}

.detailed-plan-features-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 1rem;
}

.detailed-plan-features-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.detailed-plan-feature {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0;
  font-size: 0.9rem;
  color: #a0a0a0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.detailed-plan-feature:last-child {
  border-bottom: none;
}

.detailed-plan-feature svg {
  color: #00d4aa;
  flex-shrink: 0;
}

.detailed-plan-footer {
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.detailed-plan-total-return {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detailed-plan-total-label {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
}

.detailed-plan-total-value {
  font-size: 1.5rem;
  font-weight: 700;
  font-family: 'Monaco', 'Menlo', monospace;
}

/* Responsive Design */
@media (max-width: 768px) {
  .plans-hero {
    padding: 6rem 0 3rem;
  }

  .plans-hero-title {
    font-size: 2.5rem;
  }

  .plans-hero-description {
    font-size: 1rem;
  }

  .deposit-portfolio-title {
    font-size: 2rem;
  }

  .deposit-portfolio-description {
    font-size: 1rem;
  }

  .plans-overview-title {
    font-size: 2rem;
  }

  .plans-overview-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .plan-overview-stats {
    flex-direction: column;
    gap: 1.5rem;
  }

  .plan-overview-arrow {
    transform: rotate(90deg);
  }

  .referral-bonus-title {
    font-size: 2rem;
  }

  .referral-bonus-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .detailed-plans-title {
    font-size: 2rem;
  }

  .detailed-plans-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .detailed-plan-card {
    padding: 2rem;
  }

  .detailed-plan-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .detailed-plan-name {
    font-size: 1.25rem;
  }

  .detailed-plan-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .plans-hero-container,
  .deposit-portfolio-container,
  .plans-overview-container,
  .referral-bonus-container,
  .detailed-plans-container {
    padding: 0 1rem;
  }

  .plans-hero-title {
    font-size: 2rem;
  }

  .deposit-portfolio-title,
  .plans-overview-title,
  .referral-bonus-title,
  .detailed-plans-title {
    font-size: 1.75rem;
  }

  .detailed-plan-card {
    padding: 1.5rem;
  }

  .detailed-plan-stats {
    padding: 1rem;
  }
}

/* Enhanced Mobile Responsive Styles */
@media (max-width: 768px) {
  .plans-hero {
    padding: 6rem 0 3rem;
  }

  .plans-hero-container {
    padding: 0 1rem;
  }

  .plans-hero-title {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  .plans-hero-description {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }

  /* Overview section mobile */
  .plans-overview {
    padding: 3rem 0;
  }

  .plans-overview-container {
    padding: 0 1rem;
  }

  .plans-overview-title {
    font-size: 2.5rem;
  }

  .plans-overview-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .plan-overview-card {
    padding: 2rem 1.5rem;
  }

  .plan-overview-name {
    font-size: 1.3rem;
  }

  .plan-overview-value {
    font-size: 1.8rem;
  }

  /* Detailed plans mobile */
  .detailed-plans {
    padding: 3rem 0;
  }

  .detailed-plans-container {
    padding: 0 1rem;
  }

  .detailed-plans-title {
    font-size: 2.5rem;
  }

  .detailed-plans-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .detailed-plan-card {
    padding: 2rem 1.5rem;
  }

  .detailed-plan-name {
    font-size: 1.3rem;
  }

  .detailed-plan-return {
    font-size: 2rem;
  }

  .detailed-plan-features li {
    font-size: 0.9rem;
  }

  /* Benefits mobile */
  .plans-benefits {
    padding: 3rem 0;
  }

  .plans-benefits-container {
    padding: 0 1rem;
  }

  .plans-benefits-title {
    font-size: 2.5rem;
  }

  .plans-benefits-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .benefit-card {
    padding: 2rem 1.5rem;
  }

  .benefit-title {
    font-size: 1.2rem;
  }

  .benefit-description {
    font-size: 0.95rem;
  }
}

@media (max-width: 480px) {
  .plans-hero-title,
  .plans-overview-title,
  .detailed-plans-title,
  .plans-benefits-title {
    font-size: 2rem;
  }

  .plans-hero-description {
    font-size: 0.95rem;
  }

  .plan-overview-card,
  .detailed-plan-card,
  .benefit-card {
    padding: 1.5rem 1rem;
  }

  .plan-overview-name,
  .detailed-plan-name {
    font-size: 1.2rem;
  }

  .plan-overview-value {
    font-size: 1.6rem;
  }

  .detailed-plan-return {
    font-size: 1.8rem;
  }

  .benefit-title {
    font-size: 1.1rem;
  }

  .benefit-description {
    font-size: 0.9rem;
  }
}
