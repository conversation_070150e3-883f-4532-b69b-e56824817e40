import React, { useState } from 'react';
import '../styles/Deposit.css';

const Deposit = ({ user }) => {
  const [selectedCrypto, setSelectedCrypto] = useState('bitcoin');
  const [depositAmount, setDepositAmount] = useState('');
  const [depositAddress] = useState('**********************************'); // Sample address

  const cryptoOptions = [
    {
      id: 'bitcoin',
      name: 'Bitcoin',
      symbol: 'BTC',
      icon: '₿',
      color: '#f7931a',
      network: 'Bitcoin Network',
      minDeposit: 0.001,
      confirmations: 3
    },
    {
      id: 'ethereum',
      name: 'Ethereum',
      symbol: 'ETH',
      icon: 'Ξ',
      color: '#627eea',
      network: 'Ethereum Network',
      minDeposit: 0.01,
      confirmations: 12
    },
    {
      id: 'usdt',
      name: 'Tether <PERSON>',
      symbol: 'USDT',
      icon: '₮',
      color: '#26a17b',
      network: 'ERC-20',
      minDeposit: 10,
      confirmations: 12
    },
    {
      id: 'usdc',
      name: 'USD Coin',
      symbol: 'USDC',
      icon: '$',
      color: '#2775ca',
      network: 'ERC-20',
      minDeposit: 10,
      confirmations: 12
    }
  ];

  const selectedCryptoData = cryptoOptions.find(crypto => crypto.id === selectedCrypto);

  const handleCopyAddress = () => {
    navigator.clipboard.writeText(depositAddress);
    // Show success message
  };

  const generateQRCode = (address) => {
    // In a real app, you'd use a QR code library
    return `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${address}`;
  };

  return (
    <div className="deposit">
      <div className="deposit-header">
        <h1>Deposit Funds</h1>
        <p>Add cryptocurrency to your BullSeed account</p>
      </div>

      <div className="deposit-content">
        <div className="deposit-main">
          <div className="deposit-crypto-selector">
            <h3>Select Cryptocurrency</h3>
            <div className="crypto-options">
              {cryptoOptions.map((crypto) => (
                <button
                  key={crypto.id}
                  className={`crypto-option ${selectedCrypto === crypto.id ? 'active' : ''}`}
                  onClick={() => setSelectedCrypto(crypto.id)}
                >
                  <div className="crypto-option-icon" style={{ color: crypto.color }}>
                    {crypto.icon}
                  </div>
                  <div className="crypto-option-info">
                    <div className="crypto-option-name">{crypto.name}</div>
                    <div className="crypto-option-symbol">{crypto.symbol}</div>
                  </div>
                  <div className="crypto-option-network">{crypto.network}</div>
                </button>
              ))}
            </div>
          </div>

          <div className="deposit-details">
            <div className="deposit-details-header">
              <div className="deposit-crypto-info">
                <div className="deposit-crypto-icon" style={{ color: selectedCryptoData.color }}>
                  {selectedCryptoData.icon}
                </div>
                <div>
                  <h3>{selectedCryptoData.name} Deposit</h3>
                  <p>Network: {selectedCryptoData.network}</p>
                </div>
              </div>
            </div>

            <div className="deposit-address-section">
              <h4>Deposit Address</h4>
              <div className="deposit-address-container">
                <div className="deposit-address">
                  <input
                    type="text"
                    value={depositAddress}
                    readOnly
                    className="deposit-address-input"
                  />
                  <button className="deposit-copy-btn" onClick={handleCopyAddress}>
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                      <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                    </svg>
                    Copy
                  </button>
                </div>
              </div>

              <div className="deposit-qr-code">
                <img 
                  src={generateQRCode(depositAddress)} 
                  alt="Deposit QR Code"
                  className="qr-code-image"
                />
                <p>Scan QR code to copy address</p>
              </div>
            </div>

            <div className="deposit-info">
              <div className="deposit-info-item">
                <span className="deposit-info-label">Minimum Deposit:</span>
                <span className="deposit-info-value">
                  {selectedCryptoData.minDeposit} {selectedCryptoData.symbol}
                </span>
              </div>
              <div className="deposit-info-item">
                <span className="deposit-info-label">Required Confirmations:</span>
                <span className="deposit-info-value">{selectedCryptoData.confirmations}</span>
              </div>
              <div className="deposit-info-item">
                <span className="deposit-info-label">Processing Time:</span>
                <span className="deposit-info-value">5-30 minutes</span>
              </div>
            </div>

            <div className="deposit-warnings">
              <div className="deposit-warning">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
                  <line x1="12" y1="9" x2="12" y2="13"/>
                  <line x1="12" y1="17" x2="12.01" y2="17"/>
                </svg>
                <div>
                  <strong>Important:</strong> Only send {selectedCryptoData.name} to this address. 
                  Sending other cryptocurrencies may result in permanent loss.
                </div>
              </div>
              <div className="deposit-warning">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="12" cy="12" r="10"/>
                  <path d="M9,9h6v6H9z"/>
                </svg>
                <div>
                  Deposits require {selectedCryptoData.confirmations} network confirmations before being credited to your account.
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="deposit-sidebar">
          <div className="deposit-history">
            <h3>Recent Deposits</h3>
            <div className="deposit-history-list">
              <div className="deposit-history-empty">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1">
                  <circle cx="12" cy="12" r="10"/>
                  <path d="M8 12h8"/>
                  <path d="M12 8v8"/>
                </svg>
                <p>No deposits yet</p>
                <span>Your deposit history will appear here</span>
              </div>
            </div>
          </div>

          <div className="deposit-support">
            <h3>Need Help?</h3>
            <p>Having trouble with your deposit? Our support team is here to help.</p>
            <button className="deposit-support-btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"/>
              </svg>
              Contact Support
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Deposit;
