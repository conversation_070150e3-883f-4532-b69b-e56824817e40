/* Dashboard Styles */
.dashboard {
  padding: 32px;
  max-width: 1400px;
  margin: 0 auto;
}

.dashboard-header {
  margin-bottom: 32px;
}

.dashboard-welcome h1 {
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin-bottom: 8px;
}

.dashboard-welcome p {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.dashboard-row {
  display: grid;
  gap: 24px;
  align-items: start;
}

.dashboard-row:first-child {
  grid-template-columns: 300px 1fr 280px;
}

.dashboard-row:nth-child(2) {
  grid-template-columns: 1fr 300px;
}

.dashboard-row:nth-child(3),
.dashboard-row:nth-child(4),
.dashboard-row:nth-child(5) {
  grid-template-columns: 1fr;
}

/* Stats Section */
.dashboard-stats {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.stat-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 600;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: white;
  margin-bottom: 8px;
}

.stat-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 600;
}

.stat-change.positive {
  color: #00d4aa;
}

.stat-change.negative {
  color: #dc2626;
}

.stat-change-icon {
  font-size: 12px;
}

.download-report-btn {
  background: rgba(0, 212, 170, 0.1);
  border: 1px solid rgba(0, 212, 170, 0.3);
  color: #00d4aa;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
}

.download-report-btn:hover {
  background: rgba(0, 212, 170, 0.15);
  border-color: rgba(0, 212, 170, 0.5);
}

/* Trading Chart */
.dashboard-chart {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
  height: fit-content;
}

.trading-chart {
  width: 100%;
  height: 400px;
}

.trading-chart-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.trading-chart-controls {
  display: flex;
  gap: 16px;
}

.chart-timeframes {
  display: flex;
  gap: 4px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  padding: 4px;
}

.chart-timeframe {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.chart-timeframe.active,
.chart-timeframe:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.chart-tools {
  display: flex;
  gap: 8px;
}

.chart-tool {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.chart-tool:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.trading-chart-info {
  text-align: right;
}

.chart-symbol {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 4px;
}

.chart-price {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 12px;
}

.price-current {
  font-size: 16px;
  font-weight: 700;
  color: white;
}

.price-high, .price-low {
  color: rgba(255, 255, 255, 0.7);
}

.price-change.negative {
  color: #dc2626;
}

.trading-chart-container {
  position: relative;
  height: 320px;
}

.trading-chart-canvas {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

.chart-indicators {
  position: absolute;
  top: 10px;
  left: 10px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.chart-market-data {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.market-data-item {
  display: flex;
  justify-content: space-between;
  gap: 12px;
  font-size: 12px;
}

.market-data-label {
  color: rgba(255, 255, 255, 0.6);
}

.market-data-value {
  color: white;
  font-weight: 600;
}

/* Credit Score */
.dashboard-credit-score {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
  text-align: center;
}

.credit-score-header {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 20px;
}

.credit-score-circle {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 0 auto 20px;
  background: conic-gradient(from 0deg, #00d4aa 0deg 288deg, rgba(255, 255, 255, 0.1) 288deg 360deg);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.credit-score-circle::before {
  content: '';
  position: absolute;
  width: 90px;
  height: 90px;
  background: #0a0a0a;
  border-radius: 50%;
}

.credit-score-value {
  position: relative;
  z-index: 1;
  font-size: 32px;
  font-weight: 700;
  color: white;
}

.credit-score-label {
  position: relative;
  z-index: 1;
  font-size: 14px;
  color: #00d4aa;
  font-weight: 600;
  margin-top: -8px;
}

.credit-score-info {
  text-align: center;
}

.credit-score-date {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 4px;
}

.credit-score-status {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

/* Balance Card */
.dashboard-balance {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.balance-card-header {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  padding: 20px;
}

.balance-available-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}

.balance-available-amount {
  font-size: 32px;
  font-weight: 700;
  color: white;
}

.balance-card-body {
  padding: 20px;
}

.balance-withdrawal {
  margin-bottom: 20px;
}

.balance-withdrawal-header {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 8px;
}

.balance-withdrawal-amount {
  font-size: 24px;
  font-weight: 700;
  color: white;
}

.balance-breakdown {
  margin-bottom: 20px;
}

.balance-breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  font-size: 14px;
}

.balance-breakdown-label {
  color: rgba(255, 255, 255, 0.7);
}

.balance-breakdown-value {
  color: white;
  font-weight: 600;
}

.balance-breakdown-divider {
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
  margin: 12px 0;
}

.balance-breakdown-item.total {
  font-weight: 700;
  font-size: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 12px;
  margin-top: 12px;
}

.balance-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.balance-action-btn {
  padding: 12px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  font-size: 14px;
}

.balance-action-btn.primary {
  background: rgba(71, 85, 105, 1);
  color: white;
}

.balance-action-btn.primary:hover {
  background: rgba(71, 85, 105, 0.8);
}

.balance-action-btn.secondary {
  background: transparent;
  color: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.balance-action-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.05);
  color: white;
}

/* Market Section */
.dashboard-market {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.market-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.market-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: white;
}

.market-reward {
  background: rgba(0, 212, 170, 0.1);
  color: #00d4aa;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.market-price {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.market-currency {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
}

.market-value {
  font-size: 28px;
  font-weight: 700;
  color: white;
}

.market-change {
  font-size: 14px;
  font-weight: 600;
}

.market-change.positive {
  color: #00d4aa;
}

/* Referral Card */
.dashboard-referral {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.referral-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.referral-card-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: white;
}

.referral-invite-btn {
  background: rgba(71, 85, 105, 1);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.referral-invite-btn:hover {
  background: rgba(71, 85, 105, 0.8);
}

.referral-description {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  margin-bottom: 16px;
}

.referral-link-container {
  margin-bottom: 20px;
}

.referral-link {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
}

.referral-link-text {
  flex: 1;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.referral-copy-btn {
  background: rgba(0, 212, 170, 0.1);
  border: 1px solid rgba(0, 212, 170, 0.3);
  color: #00d4aa;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.referral-copy-btn:hover {
  background: rgba(0, 212, 170, 0.15);
}

.referral-copy-btn.copied {
  background: rgba(0, 212, 170, 0.2);
  border-color: rgba(0, 212, 170, 0.5);
}

.referral-stats {
  display: flex;
  gap: 20px;
}

.referral-stat {
  flex: 1;
}

.referral-stat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.referral-stat-value {
  font-size: 24px;
  font-weight: 700;
  color: white;
  margin-bottom: 4px;
}

.referral-stat-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

/* Market Ticker */
.dashboard-ticker {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.market-ticker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.market-ticker-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: white;
}

.market-ticker-powered {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.market-ticker-scroll {
  overflow-x: auto;
  margin-bottom: 16px;
}

.market-ticker-items {
  display: flex;
  gap: 16px;
  min-width: max-content;
}

.market-ticker-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 16px;
  min-width: 200px;
}

.market-ticker-symbol {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.ticker-symbol {
  font-size: 16px;
  font-weight: 700;
  color: white;
}

.ticker-pair {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.market-ticker-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.ticker-price {
  font-size: 18px;
  font-weight: 700;
  color: white;
}

.ticker-change {
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
}

.ticker-change.positive {
  background: rgba(0, 212, 170, 0.2);
  color: #00d4aa;
}

.ticker-change.negative {
  background: rgba(220, 38, 38, 0.2);
  color: #dc2626;
}

.market-ticker-volume {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.ticker-volume-label {
  color: rgba(255, 255, 255, 0.6);
}

.ticker-volume-value {
  color: white;
  font-weight: 600;
}

.market-ticker-controls {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.ticker-control-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.ticker-control-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.market-ticker-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: rgba(255, 255, 255, 0.6);
}

/* Payment History */
.payment-history {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.payment-history h3 {
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin-bottom: 20px;
}

.payment-history-table {
  width: 100%;
}

.payment-history-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 16px;
  padding: 12px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 16px;
}

.payment-history-header .payment-col {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.payment-history-body {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.payment-history-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 16px;
  padding: 16px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  align-items: center;
}

.payment-crypto {
  display: flex;
  align-items: center;
  gap: 12px;
}

.payment-crypto-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  color: white;
  font-size: 14px;
}

.payment-crypto-icon.ada {
  background: #0033ad;
}

.payment-crypto-icon.cardano {
  background: #0033ad;
}

.payment-crypto-icon.digibyte {
  background: #006ad4;
}

.payment-crypto-icon.ethereum {
  background: #627eea;
}

.payment-crypto span {
  color: white;
  font-weight: 600;
}

.payment-col {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.payment-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.payment-status.success {
  background: rgba(0, 212, 170, 0.2);
  color: #00d4aa;
}

.payment-status.pending {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

.payment-status.failed {
  background: rgba(220, 38, 38, 0.2);
  color: #dc2626;
}

/* Mobile Responsive */
@media (max-width: 1200px) {
  .dashboard-row:first-child {
    grid-template-columns: 1fr;
  }
  
  .dashboard-row:nth-child(2) {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .dashboard {
    padding: 16px;
  }
  
  .dashboard-welcome h1 {
    font-size: 24px;
  }
  
  .dashboard-content {
    gap: 16px;
  }
  
  .dashboard-row {
    gap: 16px;
  }
  
  .stat-card {
    padding: 16px;
  }
  
  .payment-history-header,
  .payment-history-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .payment-history-header {
    display: none;
  }
  
  .payment-history-row {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 16px;
    border-bottom: none;
  }
}
