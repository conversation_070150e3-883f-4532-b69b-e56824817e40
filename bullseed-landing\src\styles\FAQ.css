/* FAQ Page Styles */
.faq-page {
  min-height: 100vh;
  background-color: #0a0a0a;
  color: #ffffff;
}

/* Hero Section */
.faq-hero {
  padding: 8rem 0 4rem;
  background: linear-gradient(135deg, #0a0a0a 0%, #111111 100%);
  position: relative;
  overflow: hidden;
}

.faq-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 25% 25%, rgba(0, 212, 170, 0.08) 0%, transparent 50%),
              radial-gradient(circle at 75% 75%, rgba(79, 70, 229, 0.08) 0%, transparent 50%);
  pointer-events: none;
}

.faq-hero-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.faq-breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 2rem;
  font-size: 0.9rem;
  color: #a0a0a0;
}

.faq-breadcrumb a {
  color: #00d4aa;
  text-decoration: none;
  transition: color 0.3s ease;
}

.faq-breadcrumb a:hover {
  color: #00b894;
}

.faq-breadcrumb span {
  color: #666;
}

.faq-hero-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.faq-hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.875rem;
  color: #a0a0a0;
  margin-bottom: 2rem;
}

.faq-hero-badge-icon {
  font-size: 1rem;
}

.faq-hero-title {
  font-size: 4rem;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 1rem;
  color: #ffffff;
}

.faq-hero-title .highlight {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.faq-hero-subtitle {
  font-size: 2.5rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 1.5rem;
}

.faq-hero-description {
  font-size: 1.25rem;
  color: #a0a0a0;
  line-height: 1.6;
  margin-bottom: 2rem;
}

/* FAQ Content Section */
.faq-content {
  padding: 6rem 0;
  background: linear-gradient(180deg, #111111 0%, #0a0a0a 100%);
  position: relative;
}

.faq-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 60% 30%, rgba(0, 212, 170, 0.03) 0%, transparent 50%),
              radial-gradient(circle at 40% 70%, rgba(79, 70, 229, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

.faq-content-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

/* Category Navigation */
.faq-categories {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 4rem;
  flex-wrap: wrap;
}

.faq-category-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #a0a0a0;
  padding: 0.75rem 1.5rem;
  border-radius: 50px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.faq-category-btn:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(0, 212, 170, 0.3);
  color: #ffffff;
  transform: translateY(-2px);
}

.faq-category-btn.active {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  border-color: transparent;
  color: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.3);
}

.faq-category-icon {
  font-size: 1rem;
}

/* FAQ Questions */
.faq-questions {
  max-width: 900px;
  margin: 0 auto;
}

.faq-item {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  margin-bottom: 1rem;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.faq-item:hover {
  background: rgba(255, 255, 255, 0.03);
  border-color: rgba(0, 212, 170, 0.2);
}

.faq-item.open {
  background: rgba(255, 255, 255, 0.04);
  border-color: rgba(0, 212, 170, 0.3);
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.1);
}

.faq-question {
  width: 100%;
  background: none;
  border: none;
  color: #ffffff;
  padding: 1.5rem 2rem;
  text-align: left;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
}

.faq-question:hover {
  background: rgba(255, 255, 255, 0.02);
}

.faq-question-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.faq-question-icon {
  color: #00d4aa;
  flex-shrink: 0;
}

.faq-question-text {
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1.4;
}

.faq-toggle-icon {
  color: #a0a0a0;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.faq-item.open .faq-toggle-icon {
  transform: rotate(180deg);
  color: #00d4aa;
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.faq-item.open .faq-answer {
  max-height: 500px;
}

.faq-answer p {
  padding: 0 2rem 2rem 4rem;
  margin: 0;
  font-size: 1rem;
  line-height: 1.6;
  color: #a0a0a0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .faq-hero {
    padding: 6rem 0 3rem;
  }

  .faq-hero-title {
    font-size: 2.5rem;
  }

  .faq-hero-subtitle {
    font-size: 1.75rem;
  }

  .faq-hero-description {
    font-size: 1rem;
  }

  .faq-categories {
    gap: 0.5rem;
  }

  .faq-category-btn {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
  }

  .faq-question {
    padding: 1.25rem 1.5rem;
  }

  .faq-question-text {
    font-size: 1rem;
  }

  .faq-answer p {
    padding: 0 1.5rem 1.5rem 3rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .faq-hero-container,
  .faq-content-container {
    padding: 0 1rem;
  }

  .faq-hero-title {
    font-size: 2rem;
  }

  .faq-hero-subtitle {
    font-size: 1.5rem;
  }

  .faq-categories {
    flex-direction: column;
    align-items: center;
  }

  .faq-category-btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }

  .faq-question {
    padding: 1rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .faq-question-content {
    width: 100%;
  }

  .faq-answer p {
    padding: 0 1rem 1rem 2.5rem;
  }
}

/* Enhanced Mobile Responsive Styles */
@media (max-width: 768px) {
  .faq-hero {
    padding: 6rem 0 3rem;
  }

  .faq-hero-container {
    padding: 0 1rem;
  }

  .faq-hero-title {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  .faq-hero-description {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }

  /* FAQ content mobile */
  .faq-content {
    padding: 3rem 0;
  }

  .faq-content-container {
    padding: 0 1rem;
  }

  .faq-categories {
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-bottom: 2rem;
  }

  .faq-category-btn {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    min-width: auto;
    flex: 1;
    min-width: 120px;
  }

  .faq-category-icon {
    font-size: 1rem;
  }

  .faq-list {
    gap: 1rem;
  }

  .faq-item {
    padding: 1.5rem;
  }

  .faq-question {
    font-size: 1.1rem;
    padding-right: 2rem;
  }

  .faq-toggle {
    width: 24px;
    height: 24px;
    font-size: 1rem;
  }

  .faq-answer {
    font-size: 0.95rem;
    padding-top: 1rem;
  }

  .faq-answer p {
    line-height: 1.6;
  }

  /* Search mobile */
  .faq-search {
    padding: 2rem 0;
  }

  .faq-search-container {
    padding: 0 1rem;
  }

  .faq-search-title {
    font-size: 2rem;
  }

  .faq-search-input {
    padding: 0.875rem 1rem 0.875rem 3rem;
    font-size: 1rem;
  }

  .faq-search-icon {
    left: 1rem;
  }
}

@media (max-width: 480px) {
  .faq-hero-title,
  .faq-search-title {
    font-size: 1.8rem;
  }

  .faq-hero-description {
    font-size: 0.95rem;
  }

  .faq-categories {
    flex-direction: column;
    gap: 0.5rem;
  }

  .faq-category-btn {
    width: 100%;
    min-width: auto;
    justify-content: flex-start;
    padding: 1rem;
  }

  .faq-item {
    padding: 1.25rem;
  }

  .faq-question {
    font-size: 1rem;
    line-height: 1.4;
  }

  .faq-toggle {
    width: 20px;
    height: 20px;
    font-size: 0.9rem;
  }

  .faq-answer {
    font-size: 0.9rem;
  }

  .faq-search-input {
    padding: 0.75rem 0.875rem 0.75rem 2.5rem;
    font-size: 0.95rem;
  }

  .faq-search-icon {
    left: 0.875rem;
    width: 16px;
    height: 16px;
  }
}
