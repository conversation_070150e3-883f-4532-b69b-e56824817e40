import React from 'react';
import { Link } from 'react-router-dom';
import '../styles/More.css';

const More = ({ user }) => {
  const menuItems = [
    {
      title: 'KYC Verification',
      items: [
        {
          icon: (
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M9 12l2 2 4-4"/>
              <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"/>
              <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"/>
              <path d="M3 12h6m6 0h6"/>
            </svg>
          ),
          title: 'KYC Status',
          description: 'Check your verification status',
          link: '/kyc-status',
          status: 'pending'
        },
        {
          icon: (
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
              <polyline points="14,2 14,8 20,8"/>
              <line x1="16" y1="13" x2="8" y2="13"/>
              <line x1="16" y1="17" x2="8" y2="17"/>
              <polyline points="10,9 9,9 8,9"/>
            </svg>
          ),
          title: 'KYC Application',
          description: 'Submit verification documents',
          link: '/kyc-application',
          status: 'action'
        }
      ]
    },
    {
      title: 'Account Management',
      items: [
        {
          icon: (
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
              <circle cx="12" cy="7" r="4"/>
            </svg>
          ),
          title: 'Profile Settings',
          description: 'Manage your personal information',
          link: '/profile',
          status: 'normal'
        },
        {
          icon: (
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <circle cx="12" cy="12" r="3"/>
              <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
            </svg>
          ),
          title: 'Account Settings',
          description: 'Security and preferences',
          link: '/account-settings',
          status: 'normal'
        }
      ]
    },
    {
      title: 'Security',
      items: [
        {
          icon: (
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
              <circle cx="12" cy="16" r="1"/>
              <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
            </svg>
          ),
          title: 'Two-Factor Authentication',
          description: 'Secure your account with 2FA',
          link: '/account-settings',
          status: 'security'
        },
        {
          icon: (
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
            </svg>
          ),
          title: 'Security Settings',
          description: 'Manage login and security options',
          link: '/account-settings',
          status: 'security'
        }
      ]
    },
    {
      title: 'Support',
      items: [
        {
          icon: (
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"/>
            </svg>
          ),
          title: 'Contact Support',
          description: '24/7 customer support',
          link: '/contact',
          status: 'support'
        },
        {
          icon: (
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <circle cx="12" cy="12" r="10"/>
              <path d="M9,9h6v6H9z"/>
            </svg>
          ),
          title: 'Help Center',
          description: 'FAQs and guides',
          link: '/learn/faq',
          status: 'support'
        }
      ]
    }
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return '#f59e0b';
      case 'action':
        return '#dc2626';
      case 'security':
        return '#7c3aed';
      case 'support':
        return '#00d4aa';
      default:
        return '#6b7280';
    }
  };

  return (
    <div className="more">
      <div className="more-header">
        <h1>More Options</h1>
        <p>Manage your account, security settings, and get support</p>
      </div>

      <div className="more-content">
        {menuItems.map((section, sectionIndex) => (
          <div key={sectionIndex} className="more-section">
            <h2 className="more-section-title">{section.title}</h2>
            <div className="more-section-items">
              {section.items.map((item, itemIndex) => (
                <Link
                  key={itemIndex}
                  to={item.link}
                  className="more-item"
                >
                  <div className="more-item-icon" style={{ color: getStatusColor(item.status) }}>
                    {item.icon}
                  </div>
                  <div className="more-item-content">
                    <h3 className="more-item-title">{item.title}</h3>
                    <p className="more-item-description">{item.description}</p>
                  </div>
                  <div className="more-item-arrow">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <polyline points="9,18 15,12 9,6"/>
                    </svg>
                  </div>
                  {item.status === 'pending' && (
                    <div className="more-item-badge">
                      <span>Pending</span>
                    </div>
                  )}
                  {item.status === 'action' && (
                    <div className="more-item-badge action">
                      <span>Action Required</span>
                    </div>
                  )}
                </Link>
              ))}
            </div>
          </div>
        ))}
      </div>

      <div className="more-footer">
        <div className="more-footer-info">
          <h3>Need Help?</h3>
          <p>Our support team is available 24/7 to assist you with any questions or concerns.</p>
          <Link to="/contact" className="more-footer-btn">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"/>
            </svg>
            Contact Support
          </Link>
        </div>
      </div>
    </div>
  );
};

export default More;
