import React from 'react';

const InvestmentPlans = () => {
  const plans = [
    {
      name: 'STANDARD PLAN',
      minDaily: '2%',
      maxProfit: '14.00%',
      icon: '📊',
      gradient: 'linear-gradient(135deg, #4f46e5, #3b82f6)',
      popular: false
    },
    {
      name: 'PROMO PLAN',
      minDaily: '4.5%',
      maxProfit: '54.00%',
      icon: '🚀',
      gradient: 'linear-gradient(135deg, #00d4aa, #00b894)',
      popular: true
    },
    {
      name: 'PREMIUM PLAN',
      minDaily: '6.4%',
      maxProfit: '128.00%',
      icon: '💎',
      gradient: 'linear-gradient(135deg, #f59e0b, #d97706)',
      popular: false
    },
    {
      name: 'GOLD-MINING-PLAN',
      minDaily: '9%',
      maxProfit: '270.00%',
      icon: '⚡',
      gradient: 'linear-gradient(135deg, #dc2626, #b91c1c)',
      popular: false
    },
    {
      name: 'BULLSEED API PLAN',
      minDaily: '17%',
      maxProfit: '510.00%',
      icon: '🔥',
      gradient: 'linear-gradient(135deg, #7c3aed, #5b21b6)',
      popular: false
    }
  ];

  const referralProgram = {
    title: 'Referral Program',
    subtitle: 'Earn rewards for every successful referral',
    commission: '2.5%',
    description: 'Receive commission for each user you refer who makes their first investment'
  };

  return (
    <section className="investment-plans">
      <div className="investment-plans-container">
        {/* Header */}
        <div className="investment-plans-header">
          <div className="investment-plans-badge">
            <span className="investment-plans-badge-icon">💰</span>
            Our Investment Offer
          </div>
          <h2 className="investment-plans-title">
            Investment <span className="highlight">Portfolio Plans</span>
          </h2>
          <p className="investment-plans-description">
            Your total income directly depends on the amount you invest. After your dashboard has been properly
            loaded, click the deposit link at the side navigation and select your plan.
          </p>
        </div>

        {/* Investment Plans Grid */}
        <div className="investment-plans-grid">
          {plans.map((plan, index) => (
            <div key={index} className={`investment-plan-card ${plan.popular ? 'popular' : ''}`}>
              {plan.popular && (
                <div className="plan-popular-badge">
                  Most Popular
                </div>
              )}
              
              <div className="plan-header">
                <div className="plan-icon" style={{ background: plan.gradient }}>
                  {plan.icon}
                </div>
                <h3 className="plan-name">{plan.name}</h3>
              </div>

              <div className="plan-returns">
                <div className="plan-return-item">
                  <span className="plan-return-label">Minimum Daily Profit</span>
                  <span className="plan-return-value" style={{ background: plan.gradient, WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>
                    {plan.minDaily}
                  </span>
                </div>
                <div className="plan-return-item">
                  <span className="plan-return-label">Maximum Profit</span>
                  <span className="plan-return-value" style={{ background: plan.gradient, WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent' }}>
                    {plan.maxProfit}
                  </span>
                </div>
              </div>


            </div>
          ))}
        </div>

        {/* Referral Program */}
        <div className="referral-program">
          <div className="referral-program-content">
            <div className="referral-program-header">
              <div className="referral-program-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                  <circle cx="9" cy="7" r="4"/>
                  <path d="M22 21v-2a4 4 0 0 0-3-3.87"/>
                  <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                </svg>
              </div>
              <div className="referral-program-text">
                <h3 className="referral-program-title">{referralProgram.title}</h3>
                <p className="referral-program-subtitle">{referralProgram.subtitle}</p>
              </div>
              <div className="referral-commission">
                <span className="referral-commission-value">{referralProgram.commission}</span>
                <span className="referral-commission-label">Commission</span>
              </div>
            </div>
            
            <p className="referral-program-description">
              {referralProgram.description}
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default InvestmentPlans;
