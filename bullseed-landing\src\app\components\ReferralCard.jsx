import React, { useState } from 'react';

const ReferralCard = ({ data }) => {
  const [copied, setCopied] = useState(false);

  const handleCopyLink = () => {
    navigator.clipboard.writeText(data.referralLink);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="referral-card">
      <div className="referral-card-header">
        <h3>Refer Us & Earn</h3>
        <button className="referral-invite-btn">
          Invite
        </button>
      </div>

      <div className="referral-card-body">
        <p className="referral-description">
          Use the below link to invite your friends.
        </p>

        <div className="referral-link-container">
          <div className="referral-link">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"/>
              <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"/>
            </svg>
            <span className="referral-link-text">{data.referralLink}</span>
          </div>
          <button 
            className={`referral-copy-btn ${copied ? 'copied' : ''}`}
            onClick={handleCopyLink}
          >
            {copied ? (
              <>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <polyline points="20,6 9,17 4,12"/>
                </svg>
                Copied!
              </>
            ) : (
              <>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                  <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                </svg>
                Copy Link
              </>
            )}
          </button>
        </div>

        <div className="referral-stats">
          <div className="referral-stat">
            <div className="referral-stat-header">
              <span>My Referral</span>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="12" cy="12" r="10"/>
                <path d="M9,9h6v6H9z"/>
              </svg>
            </div>
            <div className="referral-stat-value">{data.totalJoined}</div>
            <div className="referral-stat-label">Total Joined</div>
          </div>

          <div className="referral-stat">
            <div className="referral-stat-value">$ {data.referralEarn}</div>
            <div className="referral-stat-label">Referral Earn</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReferralCard;
