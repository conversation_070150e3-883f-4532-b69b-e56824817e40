# BullSeed App - Internal Dashboard

This is the internal BullSeed application dashboard that users access after signing up. It provides a complete crypto investment platform interface.

## Features

### 🏠 Dashboard
- Real-time portfolio overview
- Trading chart with TradingView-style interface
- Balance cards with withdrawal options
- Market ticker with live crypto data
- Payment history table
- Credit score display
- Referral program integration

### 💰 Deposit
- Multi-cryptocurrency support (BTC, ETH, USDT, USDC)
- QR code generation for deposits
- Real-time deposit tracking
- Network-specific deposit addresses
- Minimum deposit requirements
- Transaction confirmation tracking

### 📈 Invest
- 5 investment plans with different returns:
  - Standard: 2% daily, 14% total (7 days)
  - Promo: 4.5% daily, 54% total (12 days)
  - Premium: 6.4% daily, 128% total (20 days)
  - Gold-Mining: 9% daily, 270% total (30 days)
  - BullSeed API: 17% daily, 510% total (30 days)
- Investment calculator
- Plan comparison
- Investment confirmation flow

### 📊 History
- Investment history with status tracking
- Transaction history (deposits/withdrawals)
- Referral earnings history
- Export functionality
- Filterable by date ranges

### ⚙️ More Options
- KYC verification status and application
- Profile management
- Account settings
- Security settings
- Support access

### 🔐 KYC Verification
- Multi-step verification process
- Document upload (ID, proof of address, selfie)
- Real-time status tracking
- Progress indicators

### 👤 Profile & Settings
- Personal information management
- Security settings (2FA, password change)
- Notification preferences
- Session management
- Account statistics

## Technical Architecture

### Frontend Stack
- **React.js** with functional components and hooks
- **React Router** for navigation
- **CSS Modules** for styling with dark theme
- **Responsive design** for mobile and desktop

### Styling Approach
- **Dark theme** with professional aesthetic
- **Glassmorphism** effects with backdrop blur
- **Gradient accents** using BullSeed brand colors
- **Smooth animations** and transitions
- **Mobile-first** responsive design

### Component Structure
```
src/app/
├── App.jsx                 # Main app component with routing
├── components/
│   ├── Sidebar.jsx         # Navigation sidebar
│   ├── Header.jsx          # Top header with user profile
│   ├── TradingChart.jsx    # Interactive trading chart
│   ├── BalanceCard.jsx     # Account balance display
│   ├── ReferralCard.jsx    # Referral program card
│   └── MarketTicker.jsx    # Live market data ticker
├── pages/
│   ├── Dashboard.jsx       # Main dashboard page
│   ├── Deposit.jsx         # Cryptocurrency deposit page
│   ├── Invest.jsx          # Investment plans page
│   ├── History.jsx         # Transaction history page
│   ├── More.jsx            # Additional options page
│   ├── KYCStatus.jsx       # KYC verification status
│   ├── KYCApplication.jsx  # KYC application form
│   ├── Profile.jsx         # User profile management
│   └── AccountSettings.jsx # Account settings page
└── styles/
    ├── App.css             # Global app styles
    ├── Sidebar.css         # Sidebar component styles
    ├── Header.css          # Header component styles
    ├── Dashboard.css       # Dashboard page styles
    ├── Deposit.css         # Deposit page styles
    ├── Invest.css          # Investment page styles
    ├── History.css         # History page styles
    ├── More.css            # More options page styles
    ├── KYC.css             # KYC pages styles
    ├── Profile.css         # Profile page styles
    └── AccountSettings.css # Settings page styles
```

### Design System

#### Colors
- **Primary**: #00d4aa (BullSeed Green)
- **Secondary**: #00b894 (Darker Green)
- **Background**: #0a0a0a (Deep Black)
- **Surface**: rgba(255, 255, 255, 0.05) (Glass Effect)
- **Border**: rgba(255, 255, 255, 0.1) (Subtle Borders)
- **Text Primary**: #ffffff (White)
- **Text Secondary**: rgba(255, 255, 255, 0.7) (Muted White)

#### Typography
- **Font Family**: Inter, system fonts
- **Headings**: 700 weight, various sizes
- **Body**: 400-600 weight, 14-16px
- **Labels**: 600 weight, 12-14px, uppercase

#### Components
- **Cards**: Glass morphism with backdrop blur
- **Buttons**: Gradient primary, glass secondary
- **Forms**: Dark inputs with focus states
- **Tables**: Responsive with mobile adaptations
- **Navigation**: Slide-out sidebar with smooth animations

## Usage

The app is integrated into the main BullSeed website and accessible via:
- Hero section "Start Investing Now" button
- CTA section "Create Account" button
- Direct URL: `/app/*`

## Mobile Responsiveness

All components are fully responsive with:
- Mobile-first CSS approach
- Collapsible sidebar navigation
- Stacked layouts on small screens
- Touch-friendly interface elements
- Optimized typography scaling

## Future Enhancements

- Real API integration for live data
- WebSocket connections for real-time updates
- Advanced charting with TradingView widgets
- Push notifications
- Multi-language support
- Advanced security features
