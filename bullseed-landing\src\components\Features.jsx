import React from 'react';

const Features = () => {
  const features = [
    {
      icon: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M12 2L2 7l10 5 10-5-10-5z"/>
          <path d="M2 17l10 5 10-5"/>
          <path d="M2 12l10 5 10-5"/>
        </svg>
      ),
      title: 'Automated Investment Strategies',
      description: 'AI-powered algorithms execute investment strategies 24/7, optimizing your portfolio without manual intervention.'
    },
    {
      icon: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"/>
        </svg>
      ),
      title: 'Smart Portfolio Management',
      description: 'Intelligent portfolio rebalancing and diversification strategies that adapt to market conditions automatically.'
    },
    {
      icon: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
        </svg>
      ),
      title: 'Institutional-Grade Security',
      description: 'Bank-level security protocols with multi-signature wallets and cold storage to protect your investments.'
    },
    {
      icon: (
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M3 3v18h18"/>
          <path d="M7 16l4-4 4 4 6-6"/>
        </svg>
      ),
      title: 'Real-Time Performance Tracking',
      description: 'Advanced analytics dashboard with real-time performance metrics and detailed investment insights.'
    }
  ];

  return (
    <section className="features" id="features">
      <div className="features-container">
        <div className="features-header">
          <h2 className="features-title">
            Intelligent Investment<br />
            <span className="highlight">Platform & Automation</span>
          </h2>
          <p className="features-description">
            Experience automated crypto investing with AI-powered strategies designed for both new and experienced investors.
          </p>
        </div>

        <div className="features-content">
          <div className="features-left">
            <div className="features-list">
              {features.map((feature, index) => (
                <div key={index} className="feature-item">
                  <div className="feature-icon">{feature.icon}</div>
                  <div className="feature-content">
                    <h3 className="feature-title">{feature.title}</h3>
                    <p className="feature-description">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="features-right">
            <div className="dashboard-preview">
              <img src="/img 2.png" alt="BullSeed Dashboard" />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Features;
