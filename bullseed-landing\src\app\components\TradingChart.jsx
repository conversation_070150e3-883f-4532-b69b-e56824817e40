import React, { useEffect, useRef } from 'react';

const TradingChart = () => {
  const chartRef = useRef(null);

  useEffect(() => {
    // Create a simple canvas-based chart
    const canvas = chartRef.current;
    const ctx = canvas.getContext('2d');
    
    // Set canvas size
    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Generate sample data points
    const dataPoints = [];
    const numPoints = 50;
    let basePrice = 147000;
    
    for (let i = 0; i < numPoints; i++) {
      const variation = (Math.random() - 0.5) * 5000;
      basePrice += variation;
      dataPoints.push({
        x: (i / (numPoints - 1)) * canvas.width,
        y: canvas.height - ((basePrice - 140000) / 20000) * canvas.height
      });
    }

    // Draw grid lines
    ctx.strokeStyle = '#2a2a2a';
    ctx.lineWidth = 1;
    
    // Horizontal grid lines
    for (let i = 0; i <= 5; i++) {
      const y = (i / 5) * canvas.height;
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(canvas.width, y);
      ctx.stroke();
    }

    // Vertical grid lines
    for (let i = 0; i <= 8; i++) {
      const x = (i / 8) * canvas.width;
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, canvas.height);
      ctx.stroke();
    }

    // Draw price line
    ctx.strokeStyle = '#00d4aa';
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    dataPoints.forEach((point, index) => {
      if (index === 0) {
        ctx.moveTo(point.x, point.y);
      } else {
        ctx.lineTo(point.x, point.y);
      }
    });
    
    ctx.stroke();

    // Draw area under the curve
    ctx.fillStyle = 'rgba(0, 212, 170, 0.1)';
    ctx.beginPath();
    ctx.moveTo(dataPoints[0].x, canvas.height);
    
    dataPoints.forEach((point) => {
      ctx.lineTo(point.x, point.y);
    });
    
    ctx.lineTo(dataPoints[dataPoints.length - 1].x, canvas.height);
    ctx.closePath();
    ctx.fill();

    // Draw volume bars at the bottom
    ctx.fillStyle = '#4a5568';
    const barWidth = canvas.width / numPoints;
    
    for (let i = 0; i < numPoints; i++) {
      const barHeight = Math.random() * 40 + 10;
      const x = i * barWidth;
      const y = canvas.height - barHeight;
      
      ctx.fillRect(x, y, barWidth * 0.8, barHeight);
    }

    // Draw price indicators
    ctx.fillStyle = '#ffffff';
    ctx.font = '12px Inter, sans-serif';
    
    // Current price indicator
    const currentPoint = dataPoints[dataPoints.length - 1];
    ctx.fillStyle = '#00d4aa';
    ctx.beginPath();
    ctx.arc(currentPoint.x, currentPoint.y, 4, 0, 2 * Math.PI);
    ctx.fill();

    // Price labels on the right
    ctx.fillStyle = '#888888';
    ctx.textAlign = 'right';
    
    const priceLabels = ['$156,000', '$154,000', '$152,000', '$150,000', '$148,000', '$146,000'];
    priceLabels.forEach((label, index) => {
      const y = (index / (priceLabels.length - 1)) * canvas.height + 5;
      ctx.fillText(label, canvas.width - 10, y);
    });

    // Time labels at the bottom
    ctx.textAlign = 'center';
    const timeLabels = ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug'];
    timeLabels.forEach((label, index) => {
      const x = (index / (timeLabels.length - 1)) * canvas.width;
      ctx.fillText(label, x, canvas.height - 5);
    });

  }, []);

  return (
    <div className="trading-chart">
      <div className="trading-chart-header">
        <div className="trading-chart-controls">
          <div className="chart-timeframes">
            <button className="chart-timeframe">1m</button>
            <button className="chart-timeframe">30m</button>
            <button className="chart-timeframe">1h</button>
            <button className="chart-timeframe active">D</button>
          </div>
          <div className="chart-tools">
            <button className="chart-tool">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <polyline points="22,12 18,12 15,21 9,3 6,12 2,12"/>
              </svg>
            </button>
            <button className="chart-tool">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <line x1="18" y1="20" x2="18" y2="10"/>
                <line x1="12" y1="20" x2="12" y2="4"/>
                <line x1="6" y1="20" x2="6" y2="14"/>
              </svg>
            </button>
          </div>
        </div>
        
        <div className="trading-chart-info">
          <div className="chart-symbol">U.S. Dollar / Japanese Yen • 1D • FXCM</div>
          <div className="chart-price">
            <span className="price-current">147.886</span>
            <span className="price-high">H 148.663</span>
            <span className="price-low">L 147.082</span>
            <span className="price-change negative">C 147.290 -1.505 (-1.01%)</span>
          </div>
        </div>
      </div>
      
      <div className="trading-chart-container">
        <canvas ref={chartRef} className="trading-chart-canvas"></canvas>
        
        <div className="chart-indicators">
          <span className="chart-indicator">Indicators</span>
        </div>
        
        <div className="chart-market-data">
          <div className="market-data-item">
            <div className="market-data-label">Syn</div>
            <div className="market-data-value">4.2</div>
          </div>
          <div className="market-data-item">
            <div className="market-data-label">Las</div>
            <div className="market-data-value">87.11%</div>
          </div>
          <div className="market-data-item">
            <div className="market-data-label">Chg%</div>
            <div className="market-data-value">1.12B</div>
          </div>
          <div className="market-data-item">
            <div className="market-data-label">Vol</div>
            <div className="market-data-value">174.11M</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TradingChart;
