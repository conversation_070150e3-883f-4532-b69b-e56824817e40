/* More Page Styles */
.more {
  padding: 32px;
  max-width: 1000px;
  margin: 0 auto;
}

.more-header {
  margin-bottom: 32px;
  text-align: center;
}

.more-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin-bottom: 8px;
}

.more-header p {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
}

.more-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.more-section {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
  backdrop-filter: blur(10px);
}

.more-section-title {
  font-size: 20px;
  font-weight: 600;
  color: white;
  margin-bottom: 20px;
}

.more-section-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.more-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
}

.more-item:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.more-item-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
}

.more-item-content {
  flex: 1;
}

.more-item-title {
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 4px;
}

.more-item-description {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
}

.more-item-arrow {
  color: rgba(255, 255, 255, 0.4);
  transition: all 0.3s ease;
}

.more-item:hover .more-item-arrow {
  color: rgba(255, 255, 255, 0.6);
  transform: translateX(4px);
}

.more-item-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
}

.more-item-badge.action {
  background: rgba(220, 38, 38, 0.2);
  color: #dc2626;
}

.more-footer {
  margin-top: 32px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
  backdrop-filter: blur(10px);
  text-align: center;
}

.more-footer-info h3 {
  font-size: 20px;
  font-weight: 600;
  color: white;
  margin-bottom: 12px;
}

.more-footer-info p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 20px;
  line-height: 1.5;
}

.more-footer-btn {
  background: rgba(0, 212, 170, 0.1);
  border: 1px solid rgba(0, 212, 170, 0.3);
  color: #00d4aa;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
}

.more-footer-btn:hover {
  background: rgba(0, 212, 170, 0.15);
  border-color: rgba(0, 212, 170, 0.5);
  transform: translateY(-2px);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .more {
    padding: 16px;
  }
  
  .more-header h1 {
    font-size: 24px;
  }
  
  .more-section {
    padding: 20px;
  }
  
  .more-item {
    padding: 12px;
  }
  
  .more-item-icon {
    width: 36px;
    height: 36px;
  }
  
  .more-footer {
    padding: 20px;
  }
}
