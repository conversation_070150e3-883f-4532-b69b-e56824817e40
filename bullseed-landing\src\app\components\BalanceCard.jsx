import React from 'react';

const BalanceCard = ({ user }) => {
  const totalBalance = user.balance + user.earnedFunds + user.referralFunds;

  return (
    <div className="balance-card">
      <div className="balance-card-header">
        <div className="balance-available">
          <div className="balance-available-header">
            <span>Available Balance</span>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <circle cx="12" cy="12" r="10"/>
              <path d="M9,9h6v6H9z"/>
            </svg>
          </div>
          <div className="balance-available-amount">$ {user.balance.toFixed(0)}</div>
        </div>
      </div>

      <div className="balance-card-body">
        <div className="balance-withdrawal">
          <div className="balance-withdrawal-header">
            <span>Total Withdrawal</span>
          </div>
          <div className="balance-withdrawal-amount">$ {user.withdrawBalance.toFixed(0)}</div>
        </div>

        <div className="balance-breakdown">
          <div className="balance-breakdown-item">
            <span className="balance-breakdown-label">Earned Funds</span>
            <span className="balance-breakdown-value">$ {user.earnedFunds.toFixed(0)}</span>
          </div>
          
          <div className="balance-breakdown-item">
            <span className="balance-breakdown-label">Referral Funds</span>
            <span className="balance-breakdown-value">$ {user.referralFunds.toFixed(0)}</span>
          </div>
          
          <div className="balance-breakdown-divider"></div>
          
          <div className="balance-breakdown-item total">
            <span className="balance-breakdown-label">Total</span>
            <span className="balance-breakdown-value">$ {totalBalance.toFixed(0)}</span>
          </div>
        </div>

        <div className="balance-actions">
          <button className="balance-action-btn primary">
            Withdraw Funds
          </button>
          <button className="balance-action-btn secondary">
            Deposit Funds
          </button>
        </div>
      </div>
    </div>
  );
};

export default BalanceCard;
