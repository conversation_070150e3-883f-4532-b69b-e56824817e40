/* About Us Page Styles */
.about-page {
  min-height: 100vh;
  background-color: #0a0a0a;
  color: #ffffff;
}

/* Hero Section */
.about-hero {
  padding: 12rem 0 6rem;
  background: linear-gradient(180deg, #0a0a0a 0%, #111111 100%);
  position: relative;
  overflow: hidden;
}

.about-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(0, 212, 170, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.about-hero-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  position: relative;
  z-index: 1;
}

.about-breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 2rem;
  font-size: 0.875rem;
  color: #a0a0a0;
}

.about-breadcrumb a {
  color: #00d4aa;
  text-decoration: none;
  transition: color 0.3s ease;
}

.about-breadcrumb a:hover {
  color: #00b894;
}

.about-hero-title {
  font-size: 4rem;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  color: #ffffff;
}

.about-hero-title .highlight {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.about-hero-description {
  font-size: 1.25rem;
  color: #a0a0a0;
  line-height: 1.6;
  max-width: 500px;
}

.about-hero-image {
  position: relative;
}

.about-hero-image img {
  width: 100%;
  height: 400px;
  object-fit: cover;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease;
}

.about-hero-image img:hover {
  transform: scale(1.02);
}

/* Stats Section */
.about-stats {
  padding: 4rem 0;
  background-color: #0a0a0a;
  border-bottom: 1px solid #1a1a1a;
}

.about-stats-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.about-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.about-stat-card {
  text-align: center;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.about-stat-card:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(0, 212, 170, 0.3);
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 212, 170, 0.1);
}

.about-stat-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.about-stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #00d4aa, #00b894);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
  font-family: 'Monaco', 'Menlo', monospace;
}

.about-stat-label {
  font-size: 1rem;
  color: #a0a0a0;
  font-weight: 500;
}

/* Mission Section */
.about-mission {
  padding: 6rem 0;
  background: linear-gradient(135deg, #111111 0%, #0a0a0a 100%);
  position: relative;
}

.about-mission::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 70% 80%, rgba(79, 70, 229, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.about-mission-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.about-mission-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.about-mission-title {
  font-size: 3rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 2rem;
}

.about-mission-description {
  font-size: 1.125rem;
  color: #e0e0e0;
  line-height: 1.7;
  margin-bottom: 1.5rem;
}

.about-mission-image img {
  width: 100%;
  height: 350px;
  object-fit: cover;
  border-radius: 16px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

/* Values Section */
.about-values {
  padding: 6rem 0;
  background-color: #0a0a0a;
}

.about-values-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.about-values-header {
  text-align: center;
  margin-bottom: 4rem;
}

.about-values-title {
  font-size: 3rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 1rem;
}

.about-values-subtitle {
  font-size: 1.25rem;
  color: #a0a0a0;
  max-width: 600px;
  margin: 0 auto;
}

.about-values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.about-value-card {
  padding: 2.5rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  text-align: center;
}

.about-value-card:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(0, 212, 170, 0.3);
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 212, 170, 0.1);
}

.about-value-icon {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
}

.about-value-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 1rem;
}

.about-value-description {
  font-size: 1rem;
  color: #a0a0a0;
  line-height: 1.6;
}

/* Team Section */
.about-team {
  padding: 6rem 0;
  background: linear-gradient(180deg, #111111 0%, #0a0a0a 100%);
  position: relative;
}

.about-team::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 30%, rgba(0, 212, 170, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.about-team-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.about-team-header {
  text-align: center;
  margin-bottom: 4rem;
}

.about-team-title {
  font-size: 3rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 1rem;
}

.about-team-subtitle {
  font-size: 1.25rem;
  color: #a0a0a0;
  max-width: 700px;
  margin: 0 auto;
}

.about-team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.about-team-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  text-align: center;
}

.about-team-card:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(0, 212, 170, 0.3);
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 212, 170, 0.1);
}

.about-team-image {
  margin-bottom: 1.5rem;
}

.about-team-image img {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid rgba(0, 212, 170, 0.3);
  transition: all 0.3s ease;
}

.about-team-card:hover .about-team-image img {
  border-color: rgba(0, 212, 170, 0.6);
  transform: scale(1.05);
}

.about-team-name {
  font-size: 1.5rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.5rem;
}

.about-team-position {
  font-size: 1rem;
  color: #00d4aa;
  font-weight: 500;
  margin-bottom: 1rem;
}

.about-team-bio {
  font-size: 0.875rem;
  color: #a0a0a0;
  line-height: 1.6;
}

/* CTA Section */
.about-cta {
  padding: 6rem 0;
  background: linear-gradient(135deg, #111111 0%, #0a0a0a 100%);
  position: relative;
  text-align: center;
}

.about-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.02) 50%, transparent 70%),
    linear-gradient(-45deg, transparent 30%, rgba(255, 255, 255, 0.02) 50%, transparent 70%);
  pointer-events: none;
}

.about-cta-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.about-cta-title {
  font-size: 3rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 1.5rem;
}

.about-cta-description {
  font-size: 1.25rem;
  color: #a0a0a0;
  margin-bottom: 2.5rem;
  line-height: 1.6;
}

.about-cta-button {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  color: #ffffff;
  padding: 1rem 2rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.125rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  display: inline-block;
}

.about-cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .about-hero-container {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .about-hero-title {
    font-size: 2.5rem;
  }

  .about-hero-description {
    max-width: none;
  }

  .about-hero-image img {
    height: 300px;
  }

  .about-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .about-stat-card {
    padding: 1.5rem;
  }

  .about-stat-number {
    font-size: 2rem;
  }

  .about-mission-content {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .about-mission-title {
    font-size: 2.5rem;
  }

  .about-values-title {
    font-size: 2.5rem;
  }

  .about-values-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .about-value-card {
    padding: 2rem;
  }

  .about-team-title {
    font-size: 2.5rem;
  }

  .about-team-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .about-team-card {
    padding: 1.5rem;
  }

  .about-cta-title {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .about-stats-grid {
    grid-template-columns: 1fr;
  }

  .about-hero-title {
    font-size: 2rem;
  }

  .about-mission-title,
  .about-values-title,
  .about-team-title {
    font-size: 2rem;
  }

  .about-team-image img {
    width: 100px;
    height: 100px;
  }
}

/* Enhanced Mobile Responsive Styles */
@media (max-width: 768px) {
  .about-hero {
    padding: 8rem 0 4rem;
  }

  .about-hero-container {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 0 1rem;
    text-align: center;
  }

  .about-hero-title {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  .about-hero-description {
    font-size: 1rem;
    margin-bottom: 2rem;
  }

  .about-hero-buttons {
    flex-direction: column;
    gap: 1rem;
    align-items: center;
  }

  .about-cta-primary,
  .about-cta-secondary {
    width: 100%;
    max-width: 280px;
    justify-content: center;
  }

  .about-hero-image {
    order: -1;
    margin-bottom: 2rem;
  }

  .about-hero-image img {
    max-width: 300px;
  }

  /* Mission section mobile */
  .about-mission {
    padding: 4rem 0;
  }

  .about-mission-container {
    padding: 0 1rem;
  }

  .about-mission-title {
    font-size: 2.5rem;
  }

  .about-mission-description {
    font-size: 1rem;
  }

  /* Values section mobile */
  .about-values {
    padding: 4rem 0;
  }

  .about-values-container {
    padding: 0 1rem;
  }

  .about-values-title {
    font-size: 2.5rem;
  }

  .about-values-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .about-value-card {
    padding: 2rem 1.5rem;
  }

  .about-value-title {
    font-size: 1.3rem;
  }

  .about-value-description {
    font-size: 0.95rem;
  }

  /* Team section mobile */
  .about-team {
    padding: 4rem 0;
  }

  .about-team-container {
    padding: 0 1rem;
  }

  .about-team-title {
    font-size: 2.5rem;
  }

  .about-team-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .about-team-card {
    padding: 2rem 1.5rem;
  }

  .about-team-name {
    font-size: 1.2rem;
  }

  .about-team-role {
    font-size: 0.9rem;
  }

  .about-team-bio {
    font-size: 0.95rem;
  }
}

@media (max-width: 480px) {
  .about-hero-title,
  .about-mission-title,
  .about-values-title,
  .about-team-title {
    font-size: 2rem;
  }

  .about-hero-description,
  .about-mission-description {
    font-size: 0.95rem;
  }

  .about-hero-image img {
    max-width: 250px;
  }

  .about-value-card,
  .about-team-card {
    padding: 1.5rem 1rem;
  }

  .about-value-title {
    font-size: 1.2rem;
  }

  .about-value-description,
  .about-team-bio {
    font-size: 0.9rem;
  }

  .about-team-name {
    font-size: 1.1rem;
  }

  .about-team-role {
    font-size: 0.85rem;
  }
}
