/* Markets Page Styles */
.markets-page {
  background-color: #0a0a0a;
  color: #ffffff;
  min-height: 100vh;
}

/* Hero Section */
.markets-hero {
  padding: 8rem 0 4rem;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
  border-bottom: 1px solid #2a2a2a;
}

.markets-hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.markets-breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 2rem;
  font-size: 0.9rem;
  color: #888;
}

.markets-breadcrumb a {
  color: #00d4aa;
  text-decoration: none;
  transition: color 0.3s ease;
}

.markets-breadcrumb a:hover {
  color: #00b894;
}

.markets-hero-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.markets-hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(0, 212, 170, 0.1);
  border: 1px solid rgba(0, 212, 170, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.9rem;
  color: #00d4aa;
  margin-bottom: 1.5rem;
}

.markets-hero-badge-icon {
  font-size: 1.2rem;
}

.markets-hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, #ffffff 0%, #cccccc 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.markets-hero-title .highlight {
  background: linear-gradient(135deg, #00d4aa 0%, #00b894 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.markets-hero-description {
  font-size: 1.2rem;
  line-height: 1.6;
  color: #cccccc;
  max-width: 600px;
  margin: 0 auto;
}

/* Market Controls */
.markets-controls {
  padding: 2rem 0;
  background-color: #0f0f0f;
  border-bottom: 1px solid #2a2a2a;
}

.markets-controls-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}

.markets-search {
  flex: 1;
  max-width: 400px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input-wrapper svg {
  position: absolute;
  left: 1rem;
  color: #666;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 3rem;
  background-color: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  color: #ffffff;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #00d4aa;
  box-shadow: 0 0 0 3px rgba(0, 212, 170, 0.1);
}

.search-input::placeholder {
  color: #666;
}

.markets-sort {
  min-width: 200px;
}

.sort-select {
  width: 100%;
  padding: 0.75rem 1rem;
  background-color: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  color: #ffffff;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.sort-select:focus {
  outline: none;
  border-color: #00d4aa;
  box-shadow: 0 0 0 3px rgba(0, 212, 170, 0.1);
}

.sort-select option {
  background-color: #1a1a1a;
  color: #ffffff;
}

/* Market Data */
.markets-data {
  padding: 2rem 0 4rem;
  background-color: #0a0a0a;
}

.markets-data-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Loading State */
.markets-loading {
  text-align: center;
  padding: 4rem 0;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #333;
  border-top: 3px solid #00d4aa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.markets-loading p {
  color: #888;
  font-size: 1.1rem;
}

/* Error State */
.markets-error {
  text-align: center;
  padding: 4rem 0;
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.markets-error p {
  color: #ef4444;
  font-size: 1.1rem;
  margin-bottom: 2rem;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.error-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.retry-button,
.refresh-button {
  background: linear-gradient(135deg, #00d4aa 0%, #00b894 100%);
  color: #ffffff;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.refresh-button {
  background: linear-gradient(135deg, #666 0%, #555 100%);
}

.retry-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.3);
}

.refresh-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 102, 102, 0.3);
}

/* No Results State */
.markets-no-results {
  text-align: center;
  padding: 4rem 0;
}

.no-results-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.6;
}

.markets-no-results h3 {
  color: #ffffff;
  font-size: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.markets-no-results p {
  color: #888;
  font-size: 1.1rem;
  max-width: 400px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Market Table */
.markets-table-wrapper {
  overflow-x: auto;
  border-radius: 12px;
  background-color: #111111;
  border: 1px solid #2a2a2a;
}

.markets-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.95rem;
}

.markets-table th {
  background-color: #1a1a1a;
  color: #888;
  font-weight: 600;
  text-align: left;
  padding: 1rem;
  border-bottom: 1px solid #2a2a2a;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.markets-table td {
  padding: 1rem;
  border-bottom: 1px solid #1a1a1a;
  vertical-align: middle;
}

.market-row {
  transition: background-color 0.2s ease;
}

.market-row:hover {
  background-color: #151515;
}

/* Table Columns */
.rank-col, .rank-cell {
  width: 60px;
  text-align: center;
  color: #666;
  font-weight: 600;
}

.name-col, .name-cell {
  width: 200px;
}

.coin-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.coin-logo {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  flex-shrink: 0;
}

.coin-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.coin-name {
  font-weight: 600;
  color: #ffffff;
}

.coin-symbol {
  font-size: 0.85rem;
  color: #888;
  text-transform: uppercase;
}

.price-col, .price-cell {
  width: 120px;
  font-weight: 600;
  color: #ffffff;
}

.change-col, .change-cell {
  width: 80px;
  font-weight: 600;
  text-align: right;
}

.change-cell.positive {
  color: #00d4aa;
}

.change-cell.negative {
  color: #ef4444;
}

.change-cell.neutral {
  color: #888;
}

.volume-col, .volume-cell {
  width: 120px;
  text-align: right;
  color: #cccccc;
}

.marketcap-col, .marketcap-cell {
  width: 120px;
  text-align: right;
  color: #cccccc;
}

.chart-col, .chart-cell {
  width: 120px;
  text-align: center;
}

.mini-chart {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .markets-table-wrapper {
    overflow-x: scroll;
  }

  .markets-table {
    min-width: 1000px;
  }
}

@media (max-width: 768px) {
  .markets-hero {
    padding: 6rem 0 3rem;
  }

  .markets-hero-container {
    padding: 0 1rem;
  }

  .markets-hero-title {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  .markets-hero-description {
    font-size: 1rem;
    margin-bottom: 1rem;
  }

  .markets-controls {
    padding: 1.5rem 0;
  }

  .markets-controls-container {
    flex-direction: column;
    gap: 1rem;
    padding: 0 1rem;
  }

  .markets-search,
  .markets-sort {
    width: 100%;
    max-width: none;
  }

  .markets-data {
    padding: 1.5rem 0 3rem;
  }

  .markets-data-container {
    padding: 0 1rem;
  }

  /* Mobile table adjustments */
  .markets-table {
    font-size: 0.85rem;
  }

  .markets-table th,
  .markets-table td {
    padding: 0.75rem 0.5rem;
  }

  .coin-logo {
    width: 24px;
    height: 24px;
  }

  .coin-name {
    font-size: 0.9rem;
  }

  .coin-symbol {
    font-size: 0.75rem;
  }

  /* Hide some columns on mobile */
  .change-col:nth-child(4),
  .change-cell:nth-child(4),
  .change-col:nth-child(6),
  .change-cell:nth-child(6),
  .chart-col,
  .chart-cell {
    display: none;
  }

  .error-actions {
    flex-direction: column;
    align-items: center;
  }

  .retry-button,
  .refresh-button {
    width: 100%;
    max-width: 200px;
  }
}

@media (max-width: 480px) {
  .markets-hero-title {
    font-size: 2rem;
  }

  .markets-hero-description {
    font-size: 0.95rem;
  }

  .markets-table {
    font-size: 0.8rem;
  }

  .markets-table th,
  .markets-table td {
    padding: 0.5rem 0.25rem;
  }

  .coin-info {
    gap: 0.5rem;
  }

  .coin-logo {
    width: 20px;
    height: 20px;
  }

  .coin-name {
    font-size: 0.85rem;
  }

  .coin-symbol {
    font-size: 0.7rem;
  }

  /* Further hide columns on very small screens */
  .volume-col,
  .volume-cell {
    display: none;
  }

  .rank-col,
  .rank-cell {
    width: 40px;
    font-size: 0.75rem;
  }
}
