/* Contact Page Styles */
.contact-page {
  min-height: 100vh;
  background-color: #0a0a0a;
  color: #ffffff;
}

/* Hero Section */
.contact-hero {
  padding: 8rem 0 4rem;
  background: linear-gradient(135deg, #0a0a0a 0%, #111111 100%);
  position: relative;
  overflow: hidden;
}

.contact-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(0, 212, 170, 0.08) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(79, 70, 229, 0.08) 0%, transparent 50%);
  pointer-events: none;
}

.contact-hero-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.contact-breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 2rem;
  font-size: 0.9rem;
  color: #a0a0a0;
}

.contact-breadcrumb a {
  color: #00d4aa;
  text-decoration: none;
  transition: color 0.3s ease;
}

.contact-breadcrumb a:hover {
  color: #00b894;
}

.contact-breadcrumb span {
  color: #666;
}

.contact-hero-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.contact-hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.875rem;
  color: #a0a0a0;
  margin-bottom: 2rem;
}

.contact-hero-badge-icon {
  font-size: 1rem;
}

.contact-hero-title {
  font-size: 4rem;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  color: #ffffff;
}

.contact-hero-title .highlight {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.contact-hero-description {
  font-size: 1.25rem;
  color: #a0a0a0;
  line-height: 1.6;
  margin-bottom: 2rem;
}

/* Contact Content */
.contact-content {
  padding: 6rem 0;
  background: linear-gradient(180deg, #111111 0%, #0a0a0a 100%);
  position: relative;
}

.contact-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 40% 30%, rgba(0, 212, 170, 0.03) 0%, transparent 50%),
              radial-gradient(circle at 60% 70%, rgba(79, 70, 229, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

.contact-content-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

/* Contact Form */
.contact-form-section {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 24px;
  padding: 3rem;
  backdrop-filter: blur(10px);
}

.contact-form-header {
  margin-bottom: 2rem;
}

.contact-form-title {
  font-size: 2rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 1rem;
}

.contact-form-description {
  font-size: 1rem;
  color: #a0a0a0;
  line-height: 1.6;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #ffffff;
}

.form-group input,
.form-group textarea {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1rem;
  color: #ffffff;
  font-size: 1rem;
  transition: all 0.3s ease;
  resize: vertical;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #00d4aa;
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 3px rgba(0, 212, 170, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: #666;
}

.contact-submit-btn {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  color: #ffffff;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1rem;
}

.contact-submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.3);
}

/* Contact Information */
.contact-info-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.contact-info-header {
  margin-bottom: 1rem;
}

.contact-info-title {
  font-size: 2rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 1rem;
}

.contact-info-description {
  font-size: 1rem;
  color: #a0a0a0;
  line-height: 1.6;
}

.contact-info-grid {
  display: grid;
  gap: 1.5rem;
}

.contact-info-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.contact-info-card:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(0, 212, 170, 0.3);
  transform: translateY(-2px);
}

.contact-info-icon {
  font-size: 1.5rem;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 212, 170, 0.1);
  border-radius: 12px;
  flex-shrink: 0;
}

.contact-info-content {
  flex: 1;
}

.contact-info-card-title {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.25rem;
}

.contact-info-value {
  font-size: 1rem;
  font-weight: 600;
  color: #00d4aa;
  margin-bottom: 0.25rem;
}

.contact-info-card-description {
  font-size: 0.875rem;
  color: #a0a0a0;
  margin: 0;
}

/* Support Hours */
.contact-hours {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 2rem;
  backdrop-filter: blur(10px);
}

.contact-hours-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 1rem;
}

.contact-hours-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.contact-hours-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.contact-hours-item:last-child {
  border-bottom: none;
}

.contact-hours-day {
  font-size: 0.9rem;
  color: #a0a0a0;
}

.contact-hours-time {
  font-size: 0.9rem;
  font-weight: 600;
  color: #00d4aa;
}

/* Responsive Design */
@media (max-width: 768px) {
  .contact-hero {
    padding: 6rem 0 3rem;
  }

  .contact-hero-title {
    font-size: 2.5rem;
  }

  .contact-hero-description {
    font-size: 1rem;
  }

  .contact-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .contact-form-section {
    padding: 2rem;
  }

  .contact-form-title,
  .contact-info-title {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .contact-hero-container,
  .contact-content-container {
    padding: 0 1rem;
  }

  .contact-hero-title {
    font-size: 2rem;
  }

  .contact-form-section {
    padding: 1.5rem;
  }

  .contact-hours {
    padding: 1.5rem;
  }

  .contact-info-card {
    padding: 1rem;
  }
}

/* Enhanced Mobile Responsive Styles */
@media (max-width: 768px) {
  .contact-hero {
    padding: 6rem 0 3rem;
  }

  .contact-hero-container {
    padding: 0 1rem;
  }

  .contact-hero-title {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  .contact-hero-description {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }

  /* Contact form mobile */
  .contact-form {
    padding: 3rem 0;
  }

  .contact-form-container {
    padding: 0 1rem;
  }

  .contact-form-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .contact-form-title {
    font-size: 2rem;
  }

  .contact-form-description {
    font-size: 0.95rem;
  }

  .contact-form-card {
    padding: 2rem 1.5rem;
  }

  .form-group {
    margin-bottom: 1.5rem;
  }

  .form-input,
  .form-textarea {
    padding: 0.875rem;
    font-size: 1rem;
  }

  .form-submit {
    padding: 0.875rem 2rem;
    font-size: 1rem;
  }

  /* Contact info mobile */
  .contact-info {
    padding: 3rem 0;
  }

  .contact-info-container {
    padding: 0 1rem;
  }

  .contact-info-title {
    font-size: 2rem;
  }

  .contact-info-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .contact-info-card {
    padding: 2rem 1.5rem;
  }

  .contact-info-card-title {
    font-size: 1.2rem;
  }

  .contact-info-card-content {
    font-size: 0.95rem;
  }

  /* Contact hours mobile */
  .contact-hours {
    padding: 3rem 0;
  }

  .contact-hours-container {
    padding: 0 1rem;
  }

  .contact-hours-title {
    font-size: 2rem;
  }

  .contact-hours-card {
    padding: 2rem 1.5rem;
  }

  .contact-hours-list {
    gap: 1rem;
  }

  .contact-hours-item {
    font-size: 0.95rem;
  }
}

@media (max-width: 480px) {
  .contact-hero-title,
  .contact-form-title,
  .contact-info-title,
  .contact-hours-title {
    font-size: 1.8rem;
  }

  .contact-hero-description,
  .contact-form-description {
    font-size: 0.9rem;
  }

  .contact-form-card,
  .contact-info-card,
  .contact-hours-card {
    padding: 1.5rem 1rem;
  }

  .form-input,
  .form-textarea {
    padding: 0.75rem;
    font-size: 0.95rem;
  }

  .form-submit {
    padding: 0.75rem 1.5rem;
    font-size: 0.95rem;
  }

  .contact-info-card-title {
    font-size: 1.1rem;
  }

  .contact-info-card-content,
  .contact-hours-item {
    font-size: 0.9rem;
  }
}
