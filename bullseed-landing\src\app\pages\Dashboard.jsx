import React, { useState, useEffect } from 'react';
import TradingChart from '../components/TradingChart';
import BalanceCard from '../components/BalanceCard';
import ReferralCard from '../components/ReferralCard';
import MarketTicker from '../components/MarketTicker';
import '../styles/Dashboard.css';

const Dashboard = ({ user }) => {
  const [marketData, setMarketData] = useState([]);
  const [loading, setLoading] = useState(true);

  // Mock data for demonstration
  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setMarketData([
        { symbol: 'BTC/USD', price: 67684, change: -1.01, volume: '1.12B' },
        { symbol: 'ETH/USD', price: 3754.7, change: 0.10, volume: '174.11M' },
        { symbol: 'ADA/USD', price: 0.4567, change: 2.34, volume: '87.11M' },
        { symbol: 'DOT/USD', price: 7.89, change: -0.56, volume: '45.23M' }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const stats = {
    spentThisMonth: 5950.64,
    change: 2.34,
    volume: 84.42,
    marketCap: 804.42,
    avgMonthlyGrowth: 804.42
  };

  const referralData = {
    totalJoined: 0,
    referralEarn: 0,
    referralLink: 'https://www.bullseed.com/registration?ref=9656983838'
  };

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <div className="dashboard-welcome">
          <h1>Welcome back, {user.name.split(' ')[0]}</h1>
          <p>Here's a look at your performance and analytics.</p>
        </div>
      </div>

      <div className="dashboard-content">
        {/* Top Row - Stats and Chart */}
        <div className="dashboard-row">
          <div className="dashboard-stats">
            <div className="stat-card">
              <div className="stat-label">SPENT THIS MONTH</div>
              <div className="stat-value">${stats.spentThisMonth.toLocaleString()}</div>
              <div className="stat-change positive">
                <span className="stat-change-icon">↗</span>
                <span>{stats.change}%</span>
              </div>
            </div>

            <div className="stat-card">
              <div className="stat-label">VOLUME (24H)</div>
              <div className="stat-value">${stats.volume}B</div>
            </div>

            <div className="stat-card">
              <div className="stat-label">MARKET CAP</div>
              <div className="stat-value">${stats.marketCap}B</div>
            </div>

            <div className="stat-card">
              <div className="stat-label">AVG MONTHLY GROWTH</div>
              <div className="stat-value">${stats.avgMonthlyGrowth}B</div>
            </div>

            <button className="download-report-btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                <polyline points="7,10 12,15 17,10"/>
                <line x1="12" y1="15" x2="12" y2="3"/>
              </svg>
              Download Report
            </button>
          </div>

          <div className="dashboard-chart">
            <TradingChart />
          </div>

          <div className="dashboard-credit-score">
            <div className="credit-score-header">
              <span>Your credit score</span>
            </div>
            <div className="credit-score-circle">
              <div className="credit-score-value">660</div>
              <div className="credit-score-label">80%</div>
            </div>
            <div className="credit-score-info">
              <div className="credit-score-date">Last Check on 21 Apr</div>
              <div className="credit-score-status">Your credit score is average</div>
            </div>
          </div>
        </div>

        {/* Second Row - Balance and Market Data */}
        <div className="dashboard-row">
          <div className="dashboard-balance">
            <BalanceCard user={user} />
          </div>

          <div className="dashboard-market">
            <div className="market-header">
              <h3>Bitcoin</h3>
              <div className="market-reward">
                <span>Reward 6.2%</span>
              </div>
            </div>
            <div className="market-price">
              <span className="market-currency">BTC</span>
              <span className="market-value">$52,291</span>
              <span className="market-change positive">+6.2%</span>
            </div>
          </div>
        </div>

        {/* Third Row - Referral and Market Ticker */}
        <div className="dashboard-row">
          <div className="dashboard-referral">
            <ReferralCard data={referralData} />
          </div>
        </div>

        <div className="dashboard-row">
          <div className="dashboard-ticker">
            <MarketTicker data={marketData} loading={loading} />
          </div>
        </div>

        {/* Payment History */}
        <div className="dashboard-row">
          <div className="payment-history">
            <h3>Payment History</h3>
            <div className="payment-history-table">
              <div className="payment-history-header">
                <div className="payment-col">NAME</div>
                <div className="payment-col">DATE</div>
                <div className="payment-col">PRICE</div>
                <div className="payment-col">STATUS</div>
              </div>
              <div className="payment-history-body">
                <div className="payment-history-row">
                  <div className="payment-col">
                    <div className="payment-crypto">
                      <div className="payment-crypto-icon ada">A</div>
                      <span>Achain</span>
                    </div>
                  </div>
                  <div className="payment-col">12 Jun, 2024</div>
                  <div className="payment-col">$14,923.33</div>
                  <div className="payment-col">
                    <span className="payment-status success">Success</span>
                  </div>
                </div>
                
                <div className="payment-history-row">
                  <div className="payment-col">
                    <div className="payment-crypto">
                      <div className="payment-crypto-icon cardano">C</div>
                      <span>Cardano</span>
                    </div>
                  </div>
                  <div className="payment-col">16 May, 2024</div>
                  <div className="payment-col">$2,432.90</div>
                  <div className="payment-col">
                    <span className="payment-status success">Success</span>
                  </div>
                </div>
                
                <div className="payment-history-row">
                  <div className="payment-col">
                    <div className="payment-crypto">
                      <div className="payment-crypto-icon digibyte">D</div>
                      <span>Digibyte</span>
                    </div>
                  </div>
                  <div className="payment-col">21 Feb, 2024</div>
                  <div className="payment-col">$847.84</div>
                  <div className="payment-col">
                    <span className="payment-status pending">Pending</span>
                  </div>
                </div>
                
                <div className="payment-history-row">
                  <div className="payment-col">
                    <div className="payment-crypto">
                      <div className="payment-crypto-icon ethereum">E</div>
                      <span>Ethereum</span>
                    </div>
                  </div>
                  <div className="payment-col">19 Dec, 2023</div>
                  <div className="payment-col">$1,247.90</div>
                  <div className="payment-col">
                    <span className="payment-status failed">Failed</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
