/* Header Styles */
.header {
  height: 80px;
  background: rgba(26, 26, 26, 0.95);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-menu-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-menu-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.header-logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-logo-img {
  width: 28px;
  height: 28px;
  border-radius: 6px;
}

.header-logo-text {
  font-size: 18px;
  font-weight: 700;
  background: linear-gradient(135deg, #00d4aa, #00b894);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-center {
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-date-picker {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.header-date-picker:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

.header-add-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(0, 212, 170, 0.1);
  border: 1px solid rgba(0, 212, 170, 0.3);
  border-radius: 8px;
  color: #00d4aa;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.header-add-btn:hover {
  background: rgba(0, 212, 170, 0.15);
  border-color: rgba(0, 212, 170, 0.5);
}

.header-right {
  position: relative;
}

.header-profile {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.header-profile:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

.header-profile-avatar {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  overflow: hidden;
}

.header-profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.header-profile-avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #00d4aa, #00b894);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.header-profile-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.header-profile-name {
  font-size: 14px;
  font-weight: 600;
  color: white;
  line-height: 1.2;
}

.header-profile-email {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.2;
}

.header-profile-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  width: 320px;
  background: rgba(26, 26, 26, 0.98);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(20px);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  animation: slideInDown 0.3s ease-out;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.profile-dropdown-header {
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  gap: 12px;
}

.profile-dropdown-avatar {
  width: 48px;
  height: 48px;
  border-radius: 10px;
  overflow: hidden;
}

.profile-dropdown-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-dropdown-avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #00d4aa, #00b894);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.profile-dropdown-info {
  flex: 1;
}

.profile-dropdown-name {
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 4px;
}

.profile-dropdown-email {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
}

.profile-dropdown-settings {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.profile-dropdown-settings:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.profile-dropdown-balance {
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.profile-dropdown-balance-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 600;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
}

.profile-dropdown-balance-amount {
  font-size: 24px;
  font-weight: 700;
  color: white;
  margin-bottom: 12px;
}

.profile-dropdown-balance-amount span {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
}

.profile-dropdown-withdraw {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #00d4aa;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.profile-dropdown-withdraw:hover {
  color: #00b894;
}

.profile-dropdown-menu {
  padding: 12px 0;
}

.profile-dropdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.3s ease;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
  font-size: 14px;
}

.profile-dropdown-item:hover {
  background: rgba(255, 255, 255, 0.05);
  color: white;
}

/* Mobile Responsive */
@media (max-width: 1024px) {
  .header-center {
    display: none;
  }
}

@media (max-width: 768px) {
  .header {
    padding: 0 16px;
    height: 70px;
  }
  
  .header-logo-text {
    display: none;
  }
  
  .header-profile-info {
    display: none;
  }
  
  .header-profile-dropdown {
    width: 280px;
    right: -16px;
  }
}
