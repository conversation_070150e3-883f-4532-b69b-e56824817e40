/* Invest Page Styles */
.invest {
  padding: 32px;
  max-width: 1400px;
  margin: 0 auto;
}

.invest-header {
  margin-bottom: 32px;
  text-align: center;
}

.invest-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin-bottom: 8px;
}

.invest-header p {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 16px;
}

.invest-balance {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.8);
}

.invest-balance strong {
  color: #00d4aa;
  font-weight: 700;
}

.invest-content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 32px;
  align-items: start;
}

.invest-plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.invest-plan-card {
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  backdrop-filter: blur(10px);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.invest-plan-card:hover {
  transform: translateY(-4px);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
}

.invest-plan-card.selected {
  border-color: #00d4aa;
  box-shadow: 0 0 0 1px #00d4aa, 0 10px 40px rgba(0, 212, 170, 0.2);
}

.invest-plan-card.popular {
  border-color: #00d4aa;
}

.plan-popular-badge {
  position: absolute;
  top: 16px;
  right: 16px;
  background: linear-gradient(135deg, #00d4aa, #00b894);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.plan-header {
  text-align: center;
  margin-bottom: 24px;
}

.plan-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin: 0 auto 16px;
}

.plan-name {
  font-size: 18px;
  font-weight: 700;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.plan-returns {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 24px;
}

.plan-daily-return,
.plan-total-return {
  text-align: center;
}

.plan-return-value {
  display: block;
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
}

.plan-return-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.plan-details {
  margin-bottom: 20px;
}

.plan-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  font-size: 14px;
}

.plan-detail-label {
  color: rgba(255, 255, 255, 0.7);
}

.plan-detail-value {
  color: white;
  font-weight: 600;
}

.plan-features {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.plan-feature {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.plan-feature svg {
  color: #00d4aa;
  flex-shrink: 0;
}

.invest-form {
  position: sticky;
  top: 100px;
}

.invest-form-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  backdrop-filter: blur(10px);
}

.invest-form-card h3 {
  font-size: 20px;
  font-weight: 700;
  color: white;
  margin-bottom: 24px;
}

.invest-form-group {
  margin-bottom: 20px;
}

.invest-form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
}

.selected-plan-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.selected-plan-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.selected-plan-name {
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 4px;
}

.selected-plan-return {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
}

.invest-amount-input {
  width: 100%;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.invest-amount-input:focus {
  outline: none;
  border-color: #00d4aa;
  box-shadow: 0 0 0 3px rgba(0, 212, 170, 0.1);
}

.invest-amount-range {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.invest-returns-preview {
  background: rgba(0, 212, 170, 0.1);
  border: 1px solid rgba(0, 212, 170, 0.3);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.invest-returns-preview h4 {
  font-size: 16px;
  font-weight: 600;
  color: #00d4aa;
  margin-bottom: 12px;
}

.returns-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
}

.return-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.return-label {
  color: rgba(255, 255, 255, 0.7);
}

.return-value {
  color: white;
  font-weight: 600;
}

.invest-btn {
  width: 100%;
  background: linear-gradient(135deg, #00d4aa, #00b894);
  color: white;
  border: none;
  padding: 16px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.invest-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.3);
}

.invest-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.invest-confirmation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.invest-confirmation-modal {
  background: rgba(26, 26, 26, 0.98);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 32px;
  max-width: 400px;
  width: 90%;
  backdrop-filter: blur(20px);
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.invest-confirmation-modal h3 {
  font-size: 24px;
  font-weight: 700;
  color: white;
  margin-bottom: 24px;
  text-align: center;
}

.confirmation-details {
  margin-bottom: 24px;
}

.confirmation-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 14px;
}

.confirmation-item:last-child {
  border-bottom: none;
  font-weight: 700;
  font-size: 16px;
}

.confirmation-item span:first-child {
  color: rgba(255, 255, 255, 0.7);
}

.confirmation-item span:last-child {
  color: white;
  font-weight: 600;
}

.confirmation-actions {
  display: flex;
  gap: 12px;
}

.confirm-btn {
  flex: 1;
  background: linear-gradient(135deg, #00d4aa, #00b894);
  color: white;
  border: none;
  padding: 12px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.confirm-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.3);
}

.cancel-btn {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 12px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background: rgba(255, 255, 255, 0.15);
}

/* Mobile Responsive */
@media (max-width: 1024px) {
  .invest-content {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .invest-form {
    position: static;
  }
}

@media (max-width: 768px) {
  .invest {
    padding: 16px;
  }
  
  .invest-header h1 {
    font-size: 24px;
  }
  
  .invest-plans-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .invest-plan-card {
    padding: 20px;
  }
  
  .plan-returns {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .invest-form-card {
    padding: 20px;
  }
  
  .invest-confirmation-modal {
    padding: 24px;
    margin: 16px;
  }
}
