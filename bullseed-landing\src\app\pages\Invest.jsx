import React, { useState } from 'react';
import '../styles/Invest.css';

const Invest = ({ user }) => {
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [investAmount, setInvestAmount] = useState('');
  const [showConfirmation, setShowConfirmation] = useState(false);

  const investmentPlans = [
    {
      id: 'standard',
      name: 'STANDARD PLAN',
      dailyReturn: '2%',
      totalReturn: '14.00%',
      duration: '7 days',
      minDeposit: 200,
      maxDeposit: 499,
      icon: '📊',
      gradient: 'linear-gradient(135deg, #4f46e5, #3b82f6)',
      popular: false,
      features: [
        'Daily profit of 2% on invested capital',
        'Investment period of 7 days',
        'Instant withdrawals',
        '24/7 customer support'
      ]
    },
    {
      id: 'promo',
      name: 'PROMO PLAN',
      dailyReturn: '4.5%',
      totalReturn: '54.00%',
      duration: '12 days',
      minDeposit: 500,
      maxDeposit: 1499,
      icon: '🚀',
      gradient: 'linear-gradient(135deg, #00d4aa, #00b894)',
      popular: true,
      features: [
        'Daily profit of 4.5% on invested capital',
        'Investment period of 12 days',
        'Priority customer support',
        'Advanced trading algorithms'
      ]
    },
    {
      id: 'premium',
      name: 'PREMIUM PLAN',
      dailyReturn: '6.4%',
      totalReturn: '128.00%',
      duration: '20 days',
      minDeposit: 1500,
      maxDeposit: 4999,
      icon: '💎',
      gradient: 'linear-gradient(135deg, #f59e0b, #d97706)',
      popular: false,
      features: [
        'Daily profit of 6.4% on invested capital',
        'Investment period of 20 days',
        'Dedicated account manager',
        'Premium trading strategies'
      ]
    },
    {
      id: 'gold',
      name: 'GOLD-MINING PLAN',
      dailyReturn: '9%',
      totalReturn: '270.00%',
      duration: '30 days',
      minDeposit: 5000,
      maxDeposit: 9999,
      icon: '⚡',
      gradient: 'linear-gradient(135deg, #dc2626, #b91c1c)',
      popular: false,
      features: [
        'Daily profit of 9% on invested capital',
        'Investment period of 30 days',
        'VIP customer support',
        'Advanced mining algorithms'
      ]
    },
    {
      id: 'api',
      name: 'BULLSEED API PLAN',
      dailyReturn: '17%',
      totalReturn: '510.00%',
      duration: '30 days',
      minDeposit: 10000,
      maxDeposit: 100000,
      icon: '🔥',
      gradient: 'linear-gradient(135deg, #7c3aed, #5b21b6)',
      popular: false,
      features: [
        'Daily profit of 17% on invested capital',
        'Investment period of 30 days',
        'API-powered trading algorithms',
        'White-glove concierge service'
      ]
    }
  ];

  const handlePlanSelect = (plan) => {
    setSelectedPlan(plan);
    setInvestAmount(plan.minDeposit.toString());
  };

  const handleInvest = () => {
    if (!selectedPlan || !investAmount) return;
    
    const amount = parseFloat(investAmount);
    if (amount < selectedPlan.minDeposit || amount > selectedPlan.maxDeposit) {
      alert(`Investment amount must be between $${selectedPlan.minDeposit} and $${selectedPlan.maxDeposit}`);
      return;
    }

    if (amount > user.balance) {
      alert('Insufficient balance. Please deposit funds first.');
      return;
    }

    setShowConfirmation(true);
  };

  const confirmInvestment = () => {
    // Handle investment logic here
    console.log('Investment confirmed:', {
      plan: selectedPlan.name,
      amount: investAmount
    });
    setShowConfirmation(false);
    setSelectedPlan(null);
    setInvestAmount('');
  };

  const calculateReturns = () => {
    if (!selectedPlan || !investAmount) return null;
    
    const amount = parseFloat(investAmount);
    const dailyReturn = amount * (parseFloat(selectedPlan.dailyReturn) / 100);
    const totalReturn = amount * (parseFloat(selectedPlan.totalReturn) / 100);
    
    return {
      dailyReturn: dailyReturn.toFixed(2),
      totalReturn: totalReturn.toFixed(2),
      finalAmount: (amount + totalReturn).toFixed(2)
    };
  };

  const returns = calculateReturns();

  return (
    <div className="invest">
      <div className="invest-header">
        <h1>Investment Plans</h1>
        <p>Choose the perfect investment plan for your financial goals</p>
        <div className="invest-balance">
          <span>Available Balance: </span>
          <strong>${user.balance.toFixed(2)}</strong>
        </div>
      </div>

      <div className="invest-content">
        <div className="invest-plans">
          <div className="invest-plans-grid">
            {investmentPlans.map((plan) => (
              <div 
                key={plan.id} 
                className={`invest-plan-card ${selectedPlan?.id === plan.id ? 'selected' : ''} ${plan.popular ? 'popular' : ''}`}
                onClick={() => handlePlanSelect(plan)}
              >
                {plan.popular && (
                  <div className="plan-popular-badge">
                    Most Popular
                  </div>
                )}
                
                <div className="plan-header">
                  <div className="plan-icon" style={{ background: plan.gradient }}>
                    {plan.icon}
                  </div>
                  <h3 className="plan-name">{plan.name}</h3>
                </div>

                <div className="plan-returns">
                  <div className="plan-daily-return">
                    <span className="plan-return-value" style={{ 
                      background: plan.gradient, 
                      WebkitBackgroundClip: 'text', 
                      WebkitTextFillColor: 'transparent' 
                    }}>
                      {plan.dailyReturn}
                    </span>
                    <span className="plan-return-label">Daily Return</span>
                  </div>
                  <div className="plan-total-return">
                    <span className="plan-return-value">{plan.totalReturn}</span>
                    <span className="plan-return-label">Total Return</span>
                  </div>
                </div>

                <div className="plan-details">
                  <div className="plan-detail">
                    <span className="plan-detail-label">Duration:</span>
                    <span className="plan-detail-value">{plan.duration}</span>
                  </div>
                  <div className="plan-detail">
                    <span className="plan-detail-label">Min Deposit:</span>
                    <span className="plan-detail-value">${plan.minDeposit.toLocaleString()}</span>
                  </div>
                  <div className="plan-detail">
                    <span className="plan-detail-label">Max Deposit:</span>
                    <span className="plan-detail-value">${plan.maxDeposit.toLocaleString()}</span>
                  </div>
                </div>

                <div className="plan-features">
                  {plan.features.map((feature, index) => (
                    <div key={index} className="plan-feature">
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <polyline points="20,6 9,17 4,12"/>
                      </svg>
                      <span>{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {selectedPlan && (
          <div className="invest-form">
            <div className="invest-form-card">
              <h3>Investment Details</h3>
              
              <div className="invest-form-group">
                <label>Selected Plan</label>
                <div className="selected-plan-info">
                  <div className="selected-plan-icon" style={{ background: selectedPlan.gradient }}>
                    {selectedPlan.icon}
                  </div>
                  <div>
                    <div className="selected-plan-name">{selectedPlan.name}</div>
                    <div className="selected-plan-return">{selectedPlan.dailyReturn} daily • {selectedPlan.duration}</div>
                  </div>
                </div>
              </div>

              <div className="invest-form-group">
                <label>Investment Amount ($)</label>
                <input
                  type="number"
                  value={investAmount}
                  onChange={(e) => setInvestAmount(e.target.value)}
                  min={selectedPlan.minDeposit}
                  max={selectedPlan.maxDeposit}
                  placeholder={`Min: $${selectedPlan.minDeposit}`}
                  className="invest-amount-input"
                />
                <div className="invest-amount-range">
                  <span>Min: ${selectedPlan.minDeposit.toLocaleString()}</span>
                  <span>Max: ${selectedPlan.maxDeposit.toLocaleString()}</span>
                </div>
              </div>

              {returns && (
                <div className="invest-returns-preview">
                  <h4>Expected Returns</h4>
                  <div className="returns-grid">
                    <div className="return-item">
                      <span className="return-label">Daily Profit</span>
                      <span className="return-value">${returns.dailyReturn}</span>
                    </div>
                    <div className="return-item">
                      <span className="return-label">Total Profit</span>
                      <span className="return-value">${returns.totalReturn}</span>
                    </div>
                    <div className="return-item">
                      <span className="return-label">Final Amount</span>
                      <span className="return-value">${returns.finalAmount}</span>
                    </div>
                  </div>
                </div>
              )}

              <button 
                className="invest-btn"
                onClick={handleInvest}
                disabled={!investAmount || parseFloat(investAmount) < selectedPlan.minDeposit}
              >
                Invest Now
              </button>
            </div>
          </div>
        )}
      </div>

      {showConfirmation && (
        <div className="invest-confirmation-overlay">
          <div className="invest-confirmation-modal">
            <h3>Confirm Investment</h3>
            <div className="confirmation-details">
              <div className="confirmation-item">
                <span>Plan:</span>
                <span>{selectedPlan.name}</span>
              </div>
              <div className="confirmation-item">
                <span>Amount:</span>
                <span>${parseFloat(investAmount).toLocaleString()}</span>
              </div>
              <div className="confirmation-item">
                <span>Duration:</span>
                <span>{selectedPlan.duration}</span>
              </div>
              <div className="confirmation-item">
                <span>Expected Total Return:</span>
                <span>${returns.totalReturn}</span>
              </div>
            </div>
            <div className="confirmation-actions">
              <button className="confirm-btn" onClick={confirmInvestment}>
                Confirm Investment
              </button>
              <button className="cancel-btn" onClick={() => setShowConfirmation(false)}>
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Invest;
