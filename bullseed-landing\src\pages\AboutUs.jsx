import React from 'react';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import '../styles/AboutUs.css';

const AboutUs = () => {
  const stats = [
    {
      number: '36M+',
      label: 'Registered Users',
      icon: '👥'
    },
    {
      number: '178',
      label: 'Countries Supported',
      icon: '🌍'
    },
    {
      number: '$10M+',
      label: 'Monthly Withdrawals',
      icon: '💰'
    },
    {
      number: '18k+',
      label: 'Daily Active Investors',
      icon: '📈'
    }
  ];

  const teamMembers = [
    {
      name: '<PERSON>',
      position: 'Chief Executive Officer',
      image: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=400&h=400&fit=crop&crop=face',
      bio: 'Former Goldman Sachs executive with 15+ years in algorithmic trading and fintech innovation.'
    },
    {
      name: '<PERSON>',
      position: 'Chief Technology Officer',
      image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face',
      bio: 'Ex-Google engineer specializing in blockchain technology and high-frequency trading systems.'
    },
    {
      name: '<PERSON>',
      position: 'Head of Quantitative Research',
      image: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=400&h=400&fit=crop&crop=face',
      bio: 'PhD in Mathematical Finance from MIT, former quantitative analyst at Renaissance Technologies.'
    },
    {
      name: 'David Kim',
      position: 'Chief Risk Officer',
      image: 'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=400&h=400&fit=crop&crop=face',
      bio: 'Former JP Morgan risk management director with expertise in cryptocurrency market analysis.'
    },
    {
      name: 'Amara Okafor',
      position: 'Head of Operations',
      image: 'https://images.unsplash.com/photo-1531123897727-8f129e1688ce?w=400&h=400&fit=crop&crop=face',
      bio: 'Operations excellence expert from McKinsey & Company, specializing in financial services scaling.'
    },
    {
      name: 'James Thompson',
      position: 'Head of Security',
      image: 'https://images.unsplash.com/photo-**********-0b93528c311a?w=400&h=400&fit=crop&crop=face',
      bio: 'Cybersecurity veteran with 12+ years protecting financial institutions and crypto exchanges.'
    }
  ];

  const values = [
    {
      icon: '🔒',
      title: 'Security First',
      description: 'Bank-grade security protocols protect every transaction and user asset with military-level encryption.'
    },
    {
      icon: '📊',
      title: 'Data-Driven Excellence',
      description: 'Advanced algorithms and AI-powered analytics drive our investment strategies for optimal returns.'
    },
    {
      icon: '🌐',
      title: 'Global Accessibility',
      description: 'Making sophisticated investment tools accessible to everyone, regardless of location or experience.'
    },
    {
      icon: '🤝',
      title: 'Transparent Partnership',
      description: 'Complete transparency in fees, performance, and operations builds lasting trust with our community.'
    }
  ];

  return (
    <div className="about-page">
      <Navbar />
      
      {/* Hero Section */}
      <section className="about-hero">
        <div className="about-hero-container">
          <div className="about-hero-content">
            <div className="about-breadcrumb">
              <a href="/">Home</a>
              <span>/</span>
              <span>About Us</span>
            </div>
            <h1 className="about-hero-title">
              Revolutionizing <span className="highlight">Crypto Investment</span>
            </h1>
            <p className="about-hero-description">
              We're building the future of decentralized finance, making sophisticated investment 
              strategies accessible to everyone through cutting-edge technology and expert guidance.
            </p>
          </div>
          <div className="about-hero-image">
            <img src="https://images.unsplash.com/photo-1551434678-e076c223a692?w=800&h=600&fit=crop" alt="Team collaboration" />
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="about-stats">
        <div className="about-stats-container">
          <div className="about-stats-grid">
            {stats.map((stat, index) => (
              <div key={index} className="about-stat-card">
                <div className="about-stat-icon">{stat.icon}</div>
                <div className="about-stat-number">{stat.number}</div>
                <div className="about-stat-label">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="about-mission">
        <div className="about-mission-container">
          <div className="about-mission-content">
            <div className="about-mission-text">
              <h2 className="about-mission-title">Our Mission</h2>
              <p className="about-mission-description">
                At BullSeed, we believe that sophisticated investment opportunities shouldn't be 
                reserved for Wall Street elites. Our mission is to democratize access to 
                professional-grade cryptocurrency investment strategies through innovative 
                technology and algorithmic trading.
              </p>
              <p className="about-mission-description">
                We combine cutting-edge artificial intelligence, machine learning algorithms, 
                and decades of collective trading experience to deliver consistent returns 
                while minimizing risk. Our platform operates 24/7, continuously analyzing 
                market conditions and executing optimal trading strategies on behalf of our investors.
              </p>
            </div>
            <div className="about-mission-image">
              <img src="https://images.unsplash.com/photo-1559136555-9303baea8ebd?w=600&h=400&fit=crop" alt="Financial technology" />
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="about-values">
        <div className="about-values-container">
          <div className="about-values-header">
            <h2 className="about-values-title">Our Core Values</h2>
            <p className="about-values-subtitle">
              The principles that guide everything we do
            </p>
          </div>
          <div className="about-values-grid">
            {values.map((value, index) => (
              <div key={index} className="about-value-card">
                <div className="about-value-icon">{value.icon}</div>
                <h3 className="about-value-title">{value.title}</h3>
                <p className="about-value-description">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="about-team">
        <div className="about-team-container">
          <div className="about-team-header">
            <h2 className="about-team-title">Meet Our Expert Team</h2>
            <p className="about-team-subtitle">
              World-class professionals driving innovation in cryptocurrency investment
            </p>
          </div>
          <div className="about-team-grid">
            {teamMembers.map((member, index) => (
              <div key={index} className="about-team-card">
                <div className="about-team-image">
                  <img src={member.image} alt={member.name} />
                </div>
                <div className="about-team-info">
                  <h3 className="about-team-name">{member.name}</h3>
                  <p className="about-team-position">{member.position}</p>
                  <p className="about-team-bio">{member.bio}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="about-cta">
        <div className="about-cta-container">
          <div className="about-cta-content">
            <h2 className="about-cta-title">Ready to Start Your Investment Journey?</h2>
            <p className="about-cta-description">
              Join thousands of investors who trust BullSeed with their cryptocurrency investments.
            </p>
            <a href="/" className="about-cta-button">Start Investing Today →</a>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default AboutUs;
