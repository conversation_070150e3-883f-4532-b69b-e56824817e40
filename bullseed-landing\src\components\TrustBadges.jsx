import React from 'react';

const TrustBadges = () => {
  const badges = [
    {
      name: "FeatherDev",
      logo: "https://logo.clearbit.com/featherdev.com"
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      logo: "https://logo.clearbit.com/messari.io"
    },
    {
      name: "Segment",
      logo: "https://logo.clearbit.com/segment.com"
    },
    {
      name: "Layers",
      logo: "https://logo.clearbit.com/layers.com"
    },
    {
      name: "Coinbase",
      logo: "https://logo.clearbit.com/coinbase.com"
    },
    {
      name: "<PERSON>ather<PERSON><PERSON>",
      logo: "https://logo.clearbit.com/featherdev.com"
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      logo: "https://logo.clearbit.com/messari.io"
    },
    {
      name: "Segment",
      logo: "https://logo.clearbit.com/segment.com"
    },
    {
      name: "Layers",
      logo: "https://logo.clearbit.com/layers.com"
    },
    {
      name: "Coinbase",
      logo: "https://logo.clearbit.com/coinbase.com"
    }
  ];

  return (
    <section className="trust-badges">
      <div className="trust-badges-container">
        <div className="trust-badges-track">
          {badges.map((badge, index) => (
            <div key={index} className="trust-badge">
              <div className="trust-badge-icon">
                <img
                  src={badge.logo}
                  alt={badge.name}
                  onError={(e) => {
                    e.target.style.display = 'none';
                  }}
                />
              </div>
              <span className="trust-badge-name">{badge.name}</span>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default TrustBadges;
