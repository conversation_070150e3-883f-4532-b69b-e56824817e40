import React, { useState } from 'react';
import '../styles/KYC.css';

const KYCApplication = ({ user }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    // Personal Information
    firstName: '',
    lastName: '',
    dateOfBirth: '',
    nationality: '',
    phoneNumber: '',
    
    // Address Information
    address: '',
    city: '',
    state: '',
    postalCode: '',
    country: '',
    
    // Document Upload
    idType: '',
    idFront: null,
    idBack: null,
    proofOfAddress: null,
    selfie: null
  });

  const steps = [
    { id: 1, title: 'Personal Information', icon: '👤' },
    { id: 2, title: 'Address Details', icon: '🏠' },
    { id: 3, title: 'Document Upload', icon: '📄' },
    { id: 4, title: 'Review & Submit', icon: '✅' }
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleFileUpload = (field, file) => {
    setFormData(prev => ({
      ...prev,
      [field]: file
    }));
  };

  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = () => {
    // Handle form submission
    console.log('KYC Application submitted:', formData);
    alert('KYC application submitted successfully!');
  };

  const renderPersonalInfo = () => (
    <div className="kyc-form-section">
      <h3>Personal Information</h3>
      <div className="kyc-form-grid">
        <div className="kyc-form-group">
          <label>First Name *</label>
          <input
            type="text"
            value={formData.firstName}
            onChange={(e) => handleInputChange('firstName', e.target.value)}
            placeholder="Enter your first name"
            required
          />
        </div>
        <div className="kyc-form-group">
          <label>Last Name *</label>
          <input
            type="text"
            value={formData.lastName}
            onChange={(e) => handleInputChange('lastName', e.target.value)}
            placeholder="Enter your last name"
            required
          />
        </div>
        <div className="kyc-form-group">
          <label>Date of Birth *</label>
          <input
            type="date"
            value={formData.dateOfBirth}
            onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
            required
          />
        </div>
        <div className="kyc-form-group">
          <label>Nationality *</label>
          <select
            value={formData.nationality}
            onChange={(e) => handleInputChange('nationality', e.target.value)}
            required
          >
            <option value="">Select nationality</option>
            <option value="US">United States</option>
            <option value="CA">Canada</option>
            <option value="UK">United Kingdom</option>
            <option value="AU">Australia</option>
            <option value="DE">Germany</option>
            <option value="FR">France</option>
            <option value="JP">Japan</option>
            <option value="OTHER">Other</option>
          </select>
        </div>
        <div className="kyc-form-group full-width">
          <label>Phone Number *</label>
          <input
            type="tel"
            value={formData.phoneNumber}
            onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
            placeholder="+****************"
            required
          />
        </div>
      </div>
    </div>
  );

  const renderAddressInfo = () => (
    <div className="kyc-form-section">
      <h3>Address Information</h3>
      <div className="kyc-form-grid">
        <div className="kyc-form-group full-width">
          <label>Street Address *</label>
          <input
            type="text"
            value={formData.address}
            onChange={(e) => handleInputChange('address', e.target.value)}
            placeholder="Enter your street address"
            required
          />
        </div>
        <div className="kyc-form-group">
          <label>City *</label>
          <input
            type="text"
            value={formData.city}
            onChange={(e) => handleInputChange('city', e.target.value)}
            placeholder="Enter your city"
            required
          />
        </div>
        <div className="kyc-form-group">
          <label>State/Province *</label>
          <input
            type="text"
            value={formData.state}
            onChange={(e) => handleInputChange('state', e.target.value)}
            placeholder="Enter your state/province"
            required
          />
        </div>
        <div className="kyc-form-group">
          <label>Postal Code *</label>
          <input
            type="text"
            value={formData.postalCode}
            onChange={(e) => handleInputChange('postalCode', e.target.value)}
            placeholder="Enter postal code"
            required
          />
        </div>
        <div className="kyc-form-group">
          <label>Country *</label>
          <select
            value={formData.country}
            onChange={(e) => handleInputChange('country', e.target.value)}
            required
          >
            <option value="">Select country</option>
            <option value="US">United States</option>
            <option value="CA">Canada</option>
            <option value="UK">United Kingdom</option>
            <option value="AU">Australia</option>
            <option value="DE">Germany</option>
            <option value="FR">France</option>
            <option value="JP">Japan</option>
            <option value="OTHER">Other</option>
          </select>
        </div>
      </div>
    </div>
  );

  const renderDocumentUpload = () => (
    <div className="kyc-form-section">
      <h3>Document Upload</h3>
      <div className="kyc-upload-grid">
        <div className="kyc-form-group">
          <label>ID Document Type *</label>
          <select
            value={formData.idType}
            onChange={(e) => handleInputChange('idType', e.target.value)}
            required
          >
            <option value="">Select ID type</option>
            <option value="passport">Passport</option>
            <option value="drivers_license">Driver's License</option>
            <option value="national_id">National ID Card</option>
          </select>
        </div>

        <div className="kyc-upload-section">
          <div className="kyc-upload-item">
            <label>ID Document (Front) *</label>
            <div className="kyc-upload-area">
              <input
                type="file"
                accept="image/*"
                onChange={(e) => handleFileUpload('idFront', e.target.files[0])}
                id="idFront"
              />
              <label htmlFor="idFront" className="kyc-upload-label">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                  <polyline points="17,8 12,3 7,8"/>
                  <line x1="12" y1="3" x2="12" y2="15"/>
                </svg>
                <span>Upload front of ID</span>
                <small>PNG, JPG up to 10MB</small>
              </label>
              {formData.idFront && (
                <div className="kyc-upload-preview">
                  <span>✅ {formData.idFront.name}</span>
                </div>
              )}
            </div>
          </div>

          <div className="kyc-upload-item">
            <label>ID Document (Back) *</label>
            <div className="kyc-upload-area">
              <input
                type="file"
                accept="image/*"
                onChange={(e) => handleFileUpload('idBack', e.target.files[0])}
                id="idBack"
              />
              <label htmlFor="idBack" className="kyc-upload-label">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                  <polyline points="17,8 12,3 7,8"/>
                  <line x1="12" y1="3" x2="12" y2="15"/>
                </svg>
                <span>Upload back of ID</span>
                <small>PNG, JPG up to 10MB</small>
              </label>
              {formData.idBack && (
                <div className="kyc-upload-preview">
                  <span>✅ {formData.idBack.name}</span>
                </div>
              )}
            </div>
          </div>

          <div className="kyc-upload-item">
            <label>Proof of Address *</label>
            <div className="kyc-upload-area">
              <input
                type="file"
                accept="image/*,.pdf"
                onChange={(e) => handleFileUpload('proofOfAddress', e.target.files[0])}
                id="proofOfAddress"
              />
              <label htmlFor="proofOfAddress" className="kyc-upload-label">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                  <polyline points="17,8 12,3 7,8"/>
                  <line x1="12" y1="3" x2="12" y2="15"/>
                </svg>
                <span>Upload proof of address</span>
                <small>Utility bill, bank statement</small>
              </label>
              {formData.proofOfAddress && (
                <div className="kyc-upload-preview">
                  <span>✅ {formData.proofOfAddress.name}</span>
                </div>
              )}
            </div>
          </div>

          <div className="kyc-upload-item">
            <label>Selfie with ID *</label>
            <div className="kyc-upload-area">
              <input
                type="file"
                accept="image/*"
                onChange={(e) => handleFileUpload('selfie', e.target.files[0])}
                id="selfie"
              />
              <label htmlFor="selfie" className="kyc-upload-label">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                  <polyline points="17,8 12,3 7,8"/>
                  <line x1="12" y1="3" x2="12" y2="15"/>
                </svg>
                <span>Upload selfie with ID</span>
                <small>Clear photo holding your ID</small>
              </label>
              {formData.selfie && (
                <div className="kyc-upload-preview">
                  <span>✅ {formData.selfie.name}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderReview = () => (
    <div className="kyc-form-section">
      <h3>Review & Submit</h3>
      <div className="kyc-review">
        <div className="kyc-review-section">
          <h4>Personal Information</h4>
          <div className="kyc-review-item">
            <span>Name:</span>
            <span>{formData.firstName} {formData.lastName}</span>
          </div>
          <div className="kyc-review-item">
            <span>Date of Birth:</span>
            <span>{formData.dateOfBirth}</span>
          </div>
          <div className="kyc-review-item">
            <span>Nationality:</span>
            <span>{formData.nationality}</span>
          </div>
        </div>

        <div className="kyc-review-section">
          <h4>Address</h4>
          <div className="kyc-review-item">
            <span>Address:</span>
            <span>{formData.address}, {formData.city}, {formData.state} {formData.postalCode}</span>
          </div>
          <div className="kyc-review-item">
            <span>Country:</span>
            <span>{formData.country}</span>
          </div>
        </div>

        <div className="kyc-review-section">
          <h4>Documents</h4>
          <div className="kyc-review-item">
            <span>ID Type:</span>
            <span>{formData.idType}</span>
          </div>
          <div className="kyc-review-item">
            <span>Documents Uploaded:</span>
            <span>
              {formData.idFront && '✅ ID Front '}
              {formData.idBack && '✅ ID Back '}
              {formData.proofOfAddress && '✅ Proof of Address '}
              {formData.selfie && '✅ Selfie'}
            </span>
          </div>
        </div>

        <div className="kyc-terms">
          <label className="kyc-checkbox">
            <input type="checkbox" required />
            <span>I confirm that all information provided is accurate and I agree to the Terms of Service and Privacy Policy.</span>
          </label>
        </div>
      </div>
    </div>
  );

  return (
    <div className="kyc-application">
      <div className="kyc-application-header">
        <h1>KYC Verification Application</h1>
        <p>Complete your identity verification to unlock all platform features</p>
      </div>

      <div className="kyc-application-content">
        <div className="kyc-steps-progress">
          {steps.map((step) => (
            <div 
              key={step.id} 
              className={`kyc-step-item ${currentStep >= step.id ? 'active' : ''} ${currentStep > step.id ? 'completed' : ''}`}
            >
              <div className="kyc-step-icon">
                {currentStep > step.id ? '✅' : step.icon}
              </div>
              <span className="kyc-step-title">{step.title}</span>
            </div>
          ))}
        </div>

        <div className="kyc-form">
          {currentStep === 1 && renderPersonalInfo()}
          {currentStep === 2 && renderAddressInfo()}
          {currentStep === 3 && renderDocumentUpload()}
          {currentStep === 4 && renderReview()}

          <div className="kyc-form-actions">
            {currentStep > 1 && (
              <button className="kyc-btn secondary" onClick={prevStep}>
                Previous
              </button>
            )}
            {currentStep < steps.length ? (
              <button className="kyc-btn primary" onClick={nextStep}>
                Next
              </button>
            ) : (
              <button className="kyc-btn primary" onClick={handleSubmit}>
                Submit Application
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default KYCApplication;
