import React, { useState } from 'react';
import { Routes, Route } from 'react-router-dom';
import Sidebar from './components/Sidebar';
import Header from './components/Header';
import Dashboard from './pages/Dashboard';
import Deposit from './pages/Deposit';
import Invest from './pages/Invest';
import History from './pages/History';
import More from './pages/More';
import KYCStatus from './pages/KYCStatus';
import KYCApplication from './pages/KYCApplication';
import Profile from './pages/Profile';
import AccountSettings from './pages/AccountSettings';
import './styles/App.css';

const App = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [user] = useState({
    name: '<PERSON>',
    email: '<EMAIL>',
    balance: 0,
    withdrawBalance: 0,
    earnedFunds: 0,
    referralFunds: 0,
    avatar: null
  });

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <div className="app">
      <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />
      <div className={`app-main ${sidebarOpen ? 'sidebar-open' : ''}`}>
        <Header user={user} onMenuClick={toggleSidebar} />
        <main className="app-content">
          <Routes>
            <Route path="/" element={<Dashboard user={user} />} />
            <Route path="/dashboard" element={<Dashboard user={user} />} />
            <Route path="/deposit" element={<Deposit user={user} />} />
            <Route path="/invest" element={<Invest user={user} />} />
            <Route path="/history" element={<History user={user} />} />
            <Route path="/more" element={<More user={user} />} />
            <Route path="/kyc-status" element={<KYCStatus user={user} />} />
            <Route path="/kyc-application" element={<KYCApplication user={user} />} />
            <Route path="/profile" element={<Profile user={user} />} />
            <Route path="/account-settings" element={<AccountSettings user={user} />} />
          </Routes>
        </main>
      </div>
    </div>
  );
};

export default App;
