/* NFT Trade Page Styles */
.nft-trade-page {
  min-height: 100vh;
  background-color: #0a0a0a;
  color: #ffffff;
}

/* Hero Section */
.nft-hero {
  padding: 12rem 0 6rem;
  background: linear-gradient(180deg, #0a0a0a 0%, #111111 100%);
  position: relative;
  overflow: hidden;
}

.nft-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(0, 212, 170, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.nft-hero-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  position: relative;
  z-index: 1;
}

.nft-breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 2rem;
  font-size: 0.875rem;
  color: #a0a0a0;
}

.nft-breadcrumb a {
  color: #00d4aa;
  text-decoration: none;
  transition: color 0.3s ease;
}

.nft-breadcrumb a:hover {
  color: #00b894;
}

.nft-hero-title {
  font-size: 4rem;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  color: #ffffff;
}

.nft-hero-title .highlight {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nft-hero-description {
  font-size: 1.25rem;
  color: #a0a0a0;
  line-height: 1.6;
  margin-bottom: 2.5rem;
  max-width: 500px;
}

.nft-hero-buttons {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.nft-connect-wallet {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  color: #ffffff;
  border: none;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1.125rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.nft-connect-wallet:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.3);
}

.wallet-icon {
  font-size: 1.2rem;
}

.nft-explore {
  background: transparent;
  color: #ffffff;
  border: 2px solid rgba(255, 255, 255, 0.2);
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1.125rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nft-explore:hover {
  border-color: #00d4aa;
  color: #00d4aa;
  transform: translateY(-2px);
}

.nft-hero-image {
  position: relative;
}

.nft-hero-image img {
  width: 100%;
  height: 400px;
  object-fit: cover;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease;
}

.nft-hero-image img:hover {
  transform: scale(1.02);
}

/* Notable Drops Section */
.nft-notable-drops {
  padding: 6rem 0;
  background: linear-gradient(135deg, #111111 0%, #0a0a0a 100%);
  position: relative;
}

.nft-notable-drops::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 70% 80%, rgba(79, 70, 229, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.nft-notable-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.nft-section-title {
  font-size: 3rem;
  font-weight: 700;
  color: #ffffff;
  text-align: center;
  margin-bottom: 3rem;
}

.nft-drops-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.nft-drop-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.nft-drop-card:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(0, 212, 170, 0.3);
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 212, 170, 0.1);
}

.nft-drop-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.nft-drop-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.nft-drop-card:hover .nft-drop-image img {
  transform: scale(1.05);
}

.nft-drop-content {
  padding: 2rem;
}

.nft-drop-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.5rem;
}

.nft-drop-artist {
  font-size: 1rem;
  color: #00d4aa;
  margin-bottom: 1rem;
  font-weight: 500;
}

.nft-drop-description {
  font-size: 0.875rem;
  color: #a0a0a0;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.nft-drop-button {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  color: #ffffff;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.nft-drop-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.3);
}

/* Top Collections Section */
.nft-top-collections {
  padding: 6rem 0;
  background-color: #0a0a0a;
  position: relative;
}

.nft-top-collections::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 30%, rgba(0, 212, 170, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.nft-collections-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.nft-collections-header {
  text-align: center;
  margin-bottom: 4rem;
}

.nft-section-subtitle {
  font-size: 1.25rem;
  color: #a0a0a0;
  margin-top: 1rem;
}

.nft-collections-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.nft-collection-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.nft-collection-card:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(0, 212, 170, 0.3);
  transform: translateY(-3px);
  box-shadow: 0 15px 30px rgba(0, 212, 170, 0.1);
}

.nft-collection-rank {
  font-size: 1.25rem;
  font-weight: 700;
  color: #a0a0a0;
  min-width: 30px;
  text-align: center;
}

.nft-collection-image {
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: 12px;
  overflow: hidden;
  flex-shrink: 0;
}

.nft-collection-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.nft-verified {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #00d4aa;
  color: #ffffff;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 700;
}

.nft-collection-info {
  flex: 1;
}

.nft-collection-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.5rem;
}

.nft-collection-stats {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.nft-volume {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #e0e0e0;
}

.eth-icon {
  font-weight: 700;
  color: #00d4aa;
}

.volume-amount {
  font-weight: 600;
  font-family: 'Monaco', 'Menlo', monospace;
}

.nft-change {
  font-size: 0.875rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
}

.nft-change.positive {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.nft-change.negative {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.nft-view-all {
  text-align: center;
}

.nft-view-all-button {
  background: transparent;
  color: #00d4aa;
  border: 2px solid #00d4aa;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1.125rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nft-view-all-button:hover {
  background: #00d4aa;
  color: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.3);
}

/* How It Works Section */
.nft-how-it-works {
  padding: 6rem 0;
  background: linear-gradient(180deg, #111111 0%, #0a0a0a 100%);
  position: relative;
}

.nft-how-it-works::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 50%, rgba(79, 70, 229, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.nft-how-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.nft-steps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 4rem;
}

.nft-step {
  text-align: center;
  padding: 2.5rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.nft-step:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(0, 212, 170, 0.3);
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 212, 170, 0.1);
}

.nft-step-icon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
}

.nft-step-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 1rem;
}

.nft-step-description {
  font-size: 1rem;
  color: #a0a0a0;
  line-height: 1.6;
}

/* Categories Section */
.nft-categories {
  padding: 6rem 0;
  background-color: #0a0a0a;
  position: relative;
}

.nft-categories::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 80% 20%, rgba(0, 212, 170, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.nft-categories-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.nft-categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 4rem;
}

.nft-category-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  cursor: pointer;
}

.nft-category-card:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(0, 212, 170, 0.3);
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 212, 170, 0.1);
}

.nft-category-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.nft-category-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.nft-category-card:hover .nft-category-image img {
  transform: scale(1.05);
}

.nft-category-content {
  padding: 2rem;
}

.nft-category-name {
  font-size: 1.5rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.5rem;
}

.nft-category-description {
  font-size: 1rem;
  color: #a0a0a0;
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nft-hero-container {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .nft-hero-title {
    font-size: 2.5rem;
  }

  .nft-hero-description {
    max-width: none;
  }

  .nft-hero-buttons {
    flex-direction: column;
    align-items: stretch;
  }

  .nft-hero-image img {
    height: 300px;
  }

  .nft-section-title {
    font-size: 2.5rem;
  }

  .nft-drops-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .nft-collections-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .nft-collection-card {
    padding: 1rem;
  }

  .nft-steps-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .nft-step {
    padding: 2rem;
  }

  .nft-categories-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .nft-hero-title {
    font-size: 2rem;
  }

  .nft-section-title {
    font-size: 2rem;
  }

  .nft-drop-content,
  .nft-category-content {
    padding: 1.5rem;
  }

  .nft-collection-image {
    width: 50px;
    height: 50px;
  }

  .nft-collection-name {
    font-size: 1rem;
  }

  .nft-collection-stats {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* Enhanced Mobile Responsive Styles */
@media (max-width: 768px) {
  .nft-hero {
    padding: 8rem 0 4rem;
  }

  .nft-hero-container {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 0 1rem;
    text-align: center;
  }

  .nft-hero-title {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  .nft-hero-description {
    font-size: 1rem;
    margin-bottom: 2rem;
  }

  .nft-hero-buttons {
    flex-direction: column;
    gap: 1rem;
    align-items: center;
  }

  .nft-connect-wallet,
  .nft-explore {
    width: 100%;
    max-width: 280px;
    justify-content: center;
  }

  .nft-hero-image {
    order: -1;
    margin-bottom: 2rem;
  }

  .nft-hero-image img {
    max-width: 300px;
  }

  /* Collections grid mobile */
  .nft-collections-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .nft-collection-card {
    padding: 1.5rem;
  }

  /* Rankings mobile */
  .nft-rankings-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .nft-ranking-card {
    padding: 1.5rem;
  }

  /* Categories mobile */
  .nft-categories-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .nft-category-card {
    padding: 1.5rem;
  }

  .nft-category-title {
    font-size: 1.1rem;
  }

  /* Drops mobile */
  .nft-drops-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .nft-drop-card {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .nft-hero-title {
    font-size: 2rem;
  }

  .nft-hero-description {
    font-size: 0.95rem;
  }

  .nft-hero-image img {
    max-width: 250px;
  }

  .nft-categories-grid {
    grid-template-columns: 1fr;
  }

  .nft-collection-card,
  .nft-ranking-card,
  .nft-category-card,
  .nft-drop-card {
    padding: 1.25rem;
  }

  .nft-collection-name,
  .nft-category-title {
    font-size: 1rem;
  }

  .nft-collection-stats {
    font-size: 0.85rem;
  }
}
