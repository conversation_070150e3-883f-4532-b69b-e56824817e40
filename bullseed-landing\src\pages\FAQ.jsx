import React, { useState } from 'react';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import '../styles/FAQ.css';

const FAQ = () => {
  const [activeCategory, setActiveCategory] = useState('general');
  const [openQuestion, setOpenQuestion] = useState(null);

  const categories = [
    { id: 'general', name: 'General', icon: '🏢' },
    { id: 'investment', name: 'Investment', icon: '💰' },
    { id: 'financial', name: 'Financial', icon: '💳' },
    { id: 'security', name: 'Security', icon: '🔒' },
    { id: 'technical', name: 'Technical', icon: '⚙️' }
  ];

  const faqData = {
    general: [
      {
        question: "What is BullSeed?",
        answer: "BullSeed is an advanced automated investment platform specializing in cryptocurrency trading and mining activities. Our platform operates with cutting-edge algorithms and minimal human intervention, providing our clients with consistent returns through strategic crypto investments and NFT trading opportunities. We've helped thousands of investors build their financial future through our professional-grade investment solutions."
      },
      {
        question: "What do I need to become an investor?",
        answer: "To become a BullSeed investor, you simply need to create a free account through our registration process. This takes only a few minutes and requires basic personal information for verification purposes. Once registered, you'll have access to our full suite of investment plans and can begin executing your investment strategies immediately. By registering, you confirm that you are of legal age in your country of residence and comply with all local financial regulations."
      },
      {
        question: "How does BullSeed generate returns?",
        answer: "BullSeed generates returns through a diversified portfolio of cryptocurrency trading strategies, including automated trading algorithms, strategic mining operations, and NFT marketplace activities. Our experienced team of financial analysts and blockchain specialists continuously monitor market conditions to optimize performance and minimize risk across all investment vehicles."
      },
      {
        question: "Is BullSeed regulated and licensed?",
        answer: "Yes, BullSeed operates under strict regulatory compliance and maintains all necessary licenses for cryptocurrency investment services. We adhere to international financial standards and implement robust KYC (Know Your Customer) and AML (Anti-Money Laundering) procedures to ensure the highest level of legal compliance and investor protection."
      }
    ],
    investment: [
      {
        question: "What investment plans does BullSeed offer?",
        answer: "BullSeed offers five comprehensive investment plans: Standard Plan (2% daily, 14% total return), Promo Plan (4.5% daily, 54% total return), Premium Plan (6.4% daily, 128% total return), Gold-Mining Plan (9% daily, 270% total return), and our exclusive BullSeed API Plan (17% daily, 510% total return). Each plan is designed for different investment levels and risk tolerances."
      },
      {
        question: "How are daily profits calculated and distributed?",
        answer: "Daily profits are calculated based on your invested capital and the specific plan you've chosen. Returns are generated through our automated trading systems and are credited to your account balance at the end of each trading day. You can monitor your earnings in real-time through your personalized dashboard and withdraw profits at any time."
      },
      {
        question: "What is the minimum investment amount?",
        answer: "The minimum investment varies by plan: Standard Plan starts at $200, Promo Plan at $500, Premium Plan at $1,500, Gold-Mining Plan at $5,000, and BullSeed API Plan at $10,000. These minimums are designed to ensure optimal portfolio management and maximize returns for each investment tier."
      },
      {
        question: "Can I reinvest my profits automatically?",
        answer: "Yes, BullSeed offers an automatic reinvestment feature that allows you to compound your returns by automatically reinvesting your daily profits. This powerful feature can significantly accelerate your wealth building through the power of compound interest. You can enable or disable this feature at any time through your account settings."
      }
    ],
    financial: [
      {
        question: "What payment methods does BullSeed accept?",
        answer: "BullSeed accepts a wide range of payment methods including Bitcoin, Ethereum, USDT, and other major cryptocurrencies. We also accept traditional payment methods such as bank transfers and credit/debit cards (Visa and MasterCard) for your convenience. All transactions are processed securely through our encrypted payment gateway."
      },
      {
        question: "How long does it take for deposits to be credited?",
        answer: "Cryptocurrency deposits are typically credited within 3-6 network confirmations, usually taking 15-60 minutes depending on network congestion. Traditional payment methods like bank transfers may take 1-3 business days. Credit card deposits are processed instantly. You'll receive email notifications for all deposit confirmations."
      },
      {
        question: "Are there any fees for deposits or withdrawals?",
        answer: "BullSeed does not charge any fees for deposits or withdrawals. The only fees you may encounter are network fees charged by blockchain networks for cryptocurrency transactions or fees imposed by your bank or payment provider. We believe in transparent pricing with no hidden costs."
      },
      {
        question: "How can I withdraw my funds?",
        answer: "Withdrawals can be initiated through your account dashboard by navigating to the 'Withdraw' section. Simply select your preferred withdrawal method, enter the amount, and provide the necessary details (wallet address for crypto or bank details for traditional methods). Withdrawals are typically processed within 1-24 hours."
      }
    ],
    security: [
      {
        question: "How secure is my investment with BullSeed?",
        answer: "BullSeed employs bank-level security measures including 256-bit SSL encryption, two-factor authentication (2FA), cold storage for cryptocurrency assets, and regular security audits. Our platform is protected by advanced DDoS protection and intrusion detection systems. We also maintain comprehensive insurance coverage for digital assets."
      },
      {
        question: "What if I forget my password?",
        answer: "If you forget your password, simply click the 'Forgot Password' link on the login page and enter your registered email address. You'll receive a secure password reset link within minutes. For additional security, we recommend enabling two-factor authentication to protect your account from unauthorized access."
      },
      {
        question: "Do you store my credit card information?",
        answer: "No, BullSeed does not store any credit card or banking information on our servers. All payment processing is handled through PCI-compliant third-party payment processors that maintain the highest security standards. Your financial information is encrypted and processed securely without being stored in our systems."
      },
      {
        question: "How do you protect against fraud?",
        answer: "BullSeed implements multiple layers of fraud protection including advanced AI-powered transaction monitoring, identity verification, device fingerprinting, and behavioral analysis. We also maintain a dedicated security team that monitors all platform activities 24/7 and responds immediately to any suspicious activities."
      }
    ],
    technical: [
      {
        question: "What cryptocurrencies does BullSeed support?",
        answer: "BullSeed supports all major cryptocurrencies including Bitcoin (BTC), Ethereum (ETH), Tether (USDT), Binance Coin (BNB), Cardano (ADA), Solana (SOL), and many others. Our platform is continuously updated to support new and emerging cryptocurrencies based on market demand and technical feasibility."
      },
      {
        question: "Can I access BullSeed on mobile devices?",
        answer: "Yes, BullSeed is fully optimized for mobile devices and tablets. Our responsive web platform provides the same functionality as the desktop version, allowing you to monitor investments, make deposits, process withdrawals, and access all features from anywhere. We're also developing dedicated mobile apps for iOS and Android."
      },
      {
        question: "Do you offer API access for advanced users?",
        answer: "Yes, our BullSeed API Plan includes comprehensive API access for institutional clients and advanced traders. The API allows for automated trading, real-time data access, portfolio management, and integration with third-party trading tools. API documentation and support are provided exclusively to API Plan subscribers."
      },
      {
        question: "What happens if the platform experiences technical issues?",
        answer: "BullSeed maintains 99.9% uptime through redundant servers, automatic failover systems, and 24/7 technical monitoring. In the rare event of technical issues, our engineering team responds immediately to restore service. All trading activities and account balances are continuously backed up to ensure no data loss occurs."
      }
    ]
  };

  const toggleQuestion = (index) => {
    setOpenQuestion(openQuestion === index ? null : index);
  };

  return (
    <div className="faq-page">
      <Navbar />
      
      {/* Hero Section */}
      <section className="faq-hero">
        <div className="faq-hero-container">
          <div className="faq-breadcrumb">
            <a href="/">Home</a>
            <span>/</span>
            <span>FAQ</span>
          </div>
          
          <div className="faq-hero-content">
            <div className="faq-hero-badge">
              <span className="faq-hero-badge-icon">❓</span>
              Frequently Asked Questions
            </div>
            <h1 className="faq-hero-title">
              You Have <span className="highlight">Questions</span>
            </h1>
            <h2 className="faq-hero-subtitle">We Have Answers.</h2>
            <p className="faq-hero-description">
              Find comprehensive answers to all your questions about BullSeed's investment platform, 
              security measures, and services. Can't find what you're looking for? Contact our support team.
            </p>
          </div>
        </div>
      </section>

      {/* FAQ Content */}
      <section className="faq-content">
        <div className="faq-content-container">
          {/* Category Navigation */}
          <div className="faq-categories">
            {categories.map((category) => (
              <button
                key={category.id}
                className={`faq-category-btn ${activeCategory === category.id ? 'active' : ''}`}
                onClick={() => setActiveCategory(category.id)}
              >
                <span className="faq-category-icon">{category.icon}</span>
                {category.name}
              </button>
            ))}
          </div>

          {/* FAQ Questions */}
          <div className="faq-questions">
            {faqData[activeCategory].map((faq, index) => (
              <div key={index} className={`faq-item ${openQuestion === index ? 'open' : ''}`}>
                <button
                  className="faq-question"
                  onClick={() => toggleQuestion(index)}
                >
                  <div className="faq-question-content">
                    <div className="faq-question-icon">
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <circle cx="12" cy="12" r="10"/>
                        <path d="M9,12l2,2 4,-4"/>
                      </svg>
                    </div>
                    <span className="faq-question-text">{faq.question}</span>
                  </div>
                  <div className="faq-toggle-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <polyline points="6,9 12,15 18,9"></polyline>
                    </svg>
                  </div>
                </button>
                <div className="faq-answer">
                  <p>{faq.answer}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default FAQ;
