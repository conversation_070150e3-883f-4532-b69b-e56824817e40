import React from 'react';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import '../styles/Plans.css';

const Plans = () => {
  const plans = [
    {
      name: 'STANDARD PLAN',
      minDaily: '2%',
      maxProfit: '14.00%',
      duration: '7 days',
      minDeposit: '$200',
      maxDeposit: '$499',
      icon: '📊',
      gradient: 'linear-gradient(135deg, #4f46e5, #3b82f6)',
      popular: false,
      description: 'Perfect for beginners looking to start their crypto investment journey with minimal risk and steady returns.',
      features: [
        'Daily profit of 2% on invested capital',
        'Investment period of 7 days',
        'Minimum deposit: $200',
        'Maximum deposit: $499',
        'Instant withdrawals',
        '24/7 customer support'
      ]
    },
    {
      name: 'PROMO PLAN',
      minDaily: '4.5%',
      maxProfit: '54.00%',
      duration: '12 days',
      minDeposit: '$500',
      maxDeposit: '$1,499',
      icon: '🚀',
      gradient: 'linear-gradient(135deg, #00d4aa, #00b894)',
      popular: true,
      description: 'Our most popular plan offering excellent returns with balanced risk for intermediate investors.',
      features: [
        'Daily profit of 4.5% on invested capital',
        'Investment period of 12 days',
        'Minimum deposit: $500',
        'Maximum deposit: $1,499',
        'Priority customer support',
        'Advanced analytics dashboard'
      ]
    },
    {
      name: 'PREMIUM PLAN',
      minDaily: '6.4%',
      maxProfit: '128.00%',
      duration: '20 days',
      minDeposit: '$1,500',
      maxDeposit: '$4,999',
      icon: '💎',
      gradient: 'linear-gradient(135deg, #f59e0b, #d97706)',
      popular: false,
      description: 'Premium investment option for serious investors seeking higher returns with extended investment periods.',
      features: [
        'Daily profit of 6.4% on invested capital',
        'Investment period of 20 days',
        'Minimum deposit: $1,500',
        'Maximum deposit: $4,999',
        'Dedicated account manager',
        'Premium market insights'
      ]
    },
    {
      name: 'GOLD-MINING PLAN',
      minDaily: '9%',
      maxProfit: '270.00%',
      duration: '30 days',
      minDeposit: '$5,000',
      maxDeposit: '$50,000',
      icon: '⚡',
      gradient: 'linear-gradient(135deg, #dc2626, #b91c1c)',
      popular: false,
      description: 'VIP plan for high-net-worth individuals seeking maximum returns with our most exclusive investment tier.',
      features: [
        'Daily profit of 9% on invested capital',
        'Investment period of 30 days',
        'Minimum deposit: $5,000',
        'Maximum deposit: $50,000',
        'VIP customer support',
        'Exclusive market opportunities'
      ]
    },
    {
      name: 'BULLSEED API PLAN',
      minDaily: '17%',
      maxProfit: '510.00%',
      duration: '30 days',
      minDeposit: '$10,000',
      maxDeposit: '$100,000',
      icon: '🔥',
      gradient: 'linear-gradient(135deg, #7c3aed, #5b21b6)',
      popular: false,
      description: 'Our most exclusive plan leveraging advanced API trading algorithms for institutional-level returns.',
      features: [
        'Daily profit of 17% on invested capital',
        'Investment period of 30 days',
        'Minimum deposit: $10,000',
        'Maximum deposit: $100,000',
        'API-powered trading algorithms',
        'White-glove concierge service'
      ]
    }
  ];

  const referralTiers = [
    { level: '1st Level', percentage: '1%', description: 'Direct referrals' },
    { level: '2nd Level', percentage: '2%', description: 'Second-tier referrals' },
    { level: '3rd Level', percentage: '6%', description: 'Third-tier referrals' }
  ];

  return (
    <div className="plans-page">
      <Navbar />
      
      {/* Hero Section */}
      <section className="plans-hero">
        <div className="plans-hero-container">
          <div className="plans-breadcrumb">
            <a href="/">Home</a>
            <span>/</span>
            <span>Investment Plans</span>
          </div>
          
          <div className="plans-hero-content">
            <div className="plans-hero-badge">
              <span className="plans-hero-badge-icon">💰</span>
              Investment Plans
            </div>
            <h1 className="plans-hero-title">
              Our Investment <span className="highlight">Offer</span>
            </h1>
            <p className="plans-hero-description">
              Your total income directly depends on the amount you invest, so the more you contribute, 
              the more you can earn. After your dashboard has been properly loaded, click the deposit 
              link at the side navigation and select your plan.
            </p>
          </div>
        </div>
      </section>

      {/* Deposit Portfolio Section */}
      <section className="deposit-portfolio">
        <div className="deposit-portfolio-container">
          <div className="deposit-portfolio-content">
            <h2 className="deposit-portfolio-title">Deposit Portfolio</h2>
            <p className="deposit-portfolio-description">
              Your total income directly depends on the amount you invest, so the more you contribute, 
              the more you can earn. After your dashboard has been properly loaded, click the deposit 
              link at the side navigation and select your plan and make deposit to the wallet address 
              available to you and convenient for you.
            </p>
          </div>
        </div>
      </section>

      {/* Plans Overview Cards */}
      <section className="plans-overview">
        <div className="plans-overview-container">
          <h2 className="plans-overview-title">We are Offering</h2>
          <div className="plans-overview-grid">
            {plans.map((plan, index) => (
              <div key={index} className="plan-overview-card">
                <div className="plan-overview-icon" style={{ background: plan.gradient }}>
                  {plan.icon}
                </div>
                <h3 className="plan-overview-name">{plan.name}</h3>
                <div className="plan-overview-stats">
                  <div className="plan-overview-stat">
                    <span className="plan-overview-value" style={{ 
                      background: plan.gradient, 
                      WebkitBackgroundClip: 'text', 
                      WebkitTextFillColor: 'transparent' 
                    }}>
                      {plan.minDaily}
                    </span>
                    <span className="plan-overview-label">Minimum Daily Profit</span>
                  </div>
                  <div className="plan-overview-arrow">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <polyline points="9,18 15,12 9,6"></polyline>
                    </svg>
                  </div>
                  <div className="plan-overview-stat">
                    <span className="plan-overview-value" style={{ 
                      background: plan.gradient, 
                      WebkitBackgroundClip: 'text', 
                      WebkitTextFillColor: 'transparent' 
                    }}>
                      {plan.maxProfit}
                    </span>
                    <span className="plan-overview-label">Maximum Profit</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Referral Bonus Section */}
      <section className="referral-bonus">
        <div className="referral-bonus-container">
          <h2 className="referral-bonus-title">Referral Bonus</h2>
          <div className="referral-bonus-grid">
            {referralTiers.map((tier, index) => (
              <div key={index} className="referral-bonus-card">
                <div className="referral-bonus-icon">
                  <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                    <circle cx="9" cy="7" r="4"/>
                    <path d="M22 21v-2a4 4 0 0 0-3-3.87"/>
                    <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                  </svg>
                </div>
                <div className="referral-bonus-percentage">{tier.percentage}</div>
                <div className="referral-bonus-level">{tier.level}</div>
                <div className="referral-bonus-description">{tier.description}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Detailed Plans Section */}
      <section className="detailed-plans">
        <div className="detailed-plans-container">
          <h2 className="detailed-plans-title">Investment Plans</h2>
          <div className="detailed-plans-grid">
            {plans.map((plan, index) => (
              <div key={index} className={`detailed-plan-card ${plan.popular ? 'popular' : ''}`}>
                {plan.popular && (
                  <div className="detailed-plan-popular-badge">
                    Most Popular
                  </div>
                )}

                <div className="detailed-plan-header">
                  <div className="detailed-plan-icon" style={{ background: plan.gradient }}>
                    {plan.icon}
                  </div>
                  <h3 className="detailed-plan-name">{plan.name}</h3>
                  <p className="detailed-plan-description">{plan.description}</p>
                </div>

                <div className="detailed-plan-stats">
                  <div className="detailed-plan-stat">
                    <span className="detailed-plan-stat-value" style={{
                      background: plan.gradient,
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent'
                    }}>
                      {plan.minDaily}
                    </span>
                    <span className="detailed-plan-stat-label">Daily Profit</span>
                  </div>
                  <div className="detailed-plan-stat">
                    <span className="detailed-plan-stat-value" style={{
                      background: plan.gradient,
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent'
                    }}>
                      {plan.duration}
                    </span>
                    <span className="detailed-plan-stat-label">Duration</span>
                  </div>
                  <div className="detailed-plan-stat">
                    <span className="detailed-plan-stat-value" style={{
                      background: plan.gradient,
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent'
                    }}>
                      {plan.minDeposit} - {plan.maxDeposit}
                    </span>
                    <span className="detailed-plan-stat-label">Deposit Range</span>
                  </div>
                </div>

                <div className="detailed-plan-features">
                  <h4 className="detailed-plan-features-title">Plan Features</h4>
                  <ul className="detailed-plan-features-list">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="detailed-plan-feature">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <polyline points="20,6 9,17 4,12"></polyline>
                        </svg>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="detailed-plan-footer">
                  <div className="detailed-plan-total-return">
                    <span className="detailed-plan-total-label">Total Return</span>
                    <span className="detailed-plan-total-value" style={{
                      background: plan.gradient,
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent'
                    }}>
                      {plan.maxProfit}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Plans;
