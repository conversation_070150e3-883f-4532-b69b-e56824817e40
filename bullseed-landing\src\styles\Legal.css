/* Legal Pages Styles */
.legal-page {
  min-height: 100vh;
  background-color: #0a0a0a;
  color: #ffffff;
}

/* Hero Section */
.legal-hero {
  padding: 8rem 0 4rem;
  background: linear-gradient(135deg, #0a0a0a 0%, #111111 100%);
  position: relative;
  overflow: hidden;
}

.legal-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 30%, rgba(0, 212, 170, 0.06) 0%, transparent 50%),
              radial-gradient(circle at 80% 70%, rgba(79, 70, 229, 0.06) 0%, transparent 50%);
  pointer-events: none;
}

.legal-hero-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.legal-breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 2rem;
  font-size: 0.9rem;
  color: #a0a0a0;
}

.legal-breadcrumb a {
  color: #00d4aa;
  text-decoration: none;
  transition: color 0.3s ease;
}

.legal-breadcrumb a:hover {
  color: #00b894;
}

.legal-breadcrumb span {
  color: #666;
}

.legal-hero-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.legal-hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.875rem;
  color: #a0a0a0;
  margin-bottom: 2rem;
}

.legal-hero-badge-icon {
  font-size: 1rem;
}

.legal-hero-title {
  font-size: 4rem;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  color: #ffffff;
}

.legal-hero-title .highlight {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.legal-hero-description {
  font-size: 1.25rem;
  color: #a0a0a0;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.legal-hero-meta {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  font-size: 0.9rem;
  color: #666;
}

/* Legal Content */
.legal-content {
  padding: 6rem 0;
  background: linear-gradient(180deg, #111111 0%, #0a0a0a 100%);
  position: relative;
}

.legal-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 60% 20%, rgba(0, 212, 170, 0.02) 0%, transparent 50%),
              radial-gradient(circle at 40% 80%, rgba(79, 70, 229, 0.02) 0%, transparent 50%);
  pointer-events: none;
}

.legal-content-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.legal-document {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 24px;
  padding: 3rem;
  backdrop-filter: blur(10px);
}

.legal-introduction {
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.legal-introduction h2 {
  font-size: 1.75rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 1rem;
}

.legal-introduction p {
  font-size: 1.125rem;
  color: #a0a0a0;
  line-height: 1.7;
  margin: 0;
}

.legal-sections {
  display: flex;
  flex-direction: column;
  gap: 2.5rem;
}

.legal-section {
  padding: 1.5rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.legal-section:last-child {
  border-bottom: none;
}

.legal-section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.legal-section-title::before {
  content: '';
  width: 4px;
  height: 20px;
  background: linear-gradient(135deg, #00d4aa, #00b894);
  border-radius: 2px;
  flex-shrink: 0;
}

.legal-section-content {
  font-size: 1rem;
  color: #a0a0a0;
  line-height: 1.7;
  margin: 0;
  text-align: justify;
}

.legal-footer-note {
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.legal-footer-card {
  background: rgba(0, 212, 170, 0.05);
  border: 1px solid rgba(0, 212, 170, 0.2);
  border-radius: 16px;
  padding: 2rem;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.legal-footer-icon {
  color: #00d4aa;
  flex-shrink: 0;
  margin-top: 0.25rem;
}

.legal-footer-content h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.5rem;
}

.legal-footer-content p {
  font-size: 0.95rem;
  color: #a0a0a0;
  line-height: 1.6;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .legal-hero {
    padding: 6rem 0 3rem;
  }

  .legal-hero-title {
    font-size: 2.5rem;
  }

  .legal-hero-description {
    font-size: 1rem;
  }

  .legal-hero-meta {
    flex-direction: column;
    gap: 0.5rem;
  }

  .legal-document {
    padding: 2rem;
  }

  .legal-introduction h2 {
    font-size: 1.5rem;
  }

  .legal-introduction p {
    font-size: 1rem;
  }

  .legal-section-title {
    font-size: 1.125rem;
  }

  .legal-section-content {
    font-size: 0.95rem;
  }

  .legal-footer-card {
    padding: 1.5rem;
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .legal-hero-container,
  .legal-content-container {
    padding: 0 1rem;
  }

  .legal-hero-title {
    font-size: 2rem;
  }

  .legal-document {
    padding: 1.5rem;
  }

  .legal-sections {
    gap: 2rem;
  }

  .legal-section {
    padding: 1rem 0;
  }

  .legal-section-content {
    text-align: left;
  }
}
