import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import './App.css'
import Home from './pages/Home'
import AboutUs from './pages/AboutUs'
import NFTTrade from './pages/NFTTrade'
import Plans from './pages/Plans'
import FAQ from './pages/FAQ'
import Contact from './pages/Contact'
import TermsOfService from './pages/TermsOfService'
import PrivacyPolicy from './pages/PrivacyPolicy'
import Markets from './pages/Markets'

function App() {
  return (
    <Router>
      <div className="App">
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/about-us" element={<AboutUs />} />
          <Route path="/nft-trade" element={<NFTTrade />} />
          <Route path="/learn/plans" element={<Plans />} />
          <Route path="/learn/faq" element={<FAQ />} />
          <Route path="/contact" element={<Contact />} />
          <Route path="/markets" element={<Markets />} />
          <Route path="/terms-of-service" element={<TermsOfService />} />
          <Route path="/privacy-policy" element={<PrivacyPolicy />} />
        </Routes>
      </div>
    </Router>
  )
}

export default App
