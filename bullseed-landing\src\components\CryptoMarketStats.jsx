import React, { useState, useEffect } from 'react';

const CryptoMarketStats = () => {
  const [cryptoData, setCryptoData] = useState([]);
  const [loading, setLoading] = useState(true);

  // Fetch real crypto data from CoinGecko API
  useEffect(() => {
    const fetchCryptoData = async () => {
      try {
        const response = await fetch(
          'https://api.coingecko.com/api/v3/coins/markets?vs_currency=usd&order=market_cap_desc&per_page=20&page=1&sparkline=false&price_change_percentage=24h'
        );
        const data = await response.json();
        
        // Format the data for our component
        const formattedData = data.map(coin => ({
          id: coin.id,
          symbol: coin.symbol.toUpperCase(),
          name: coin.name,
          price: coin.current_price,
          change: coin.price_change_percentage_24h,
          logo: coin.image,
          marketCap: coin.market_cap
        }));
        
        // Duplicate data for seamless scrolling
        setCryptoData([...formattedData, ...formattedData]);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching crypto data:', error);
        setLoading(false);
      }
    };

    fetchCryptoData();
    
    // Update data every 30 seconds
    const interval = setInterval(fetchCryptoData, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const formatPrice = (price) => {
    if (price < 1) {
      return `$${price.toFixed(6)}`;
    } else if (price < 100) {
      return `$${price.toFixed(4)}`;
    } else {
      return `$${price.toFixed(2)}`;
    }
  };

  const formatChange = (change) => {
    const sign = change >= 0 ? '+' : '';
    return `${sign}${change.toFixed(2)}%`;
  };

  if (loading) {
    return (
      <section className="crypto-market-stats">
        <div className="crypto-market-container">
          <div className="crypto-loading">Loading market data...</div>
        </div>
      </section>
    );
  }

  return (
    <section className="crypto-market-stats">
      <div className="crypto-market-container">
        <div className="crypto-market-track">
          {cryptoData.map((coin, index) => (
            <div key={`${coin.id}-${index}`} className="crypto-stat-card">
              <div className="crypto-stat-icon">
                <img 
                  src={coin.logo} 
                  alt={coin.name}
                  onError={(e) => {
                    e.target.style.display = 'none';
                  }}
                />
              </div>
              <div className="crypto-stat-info">
                <div className="crypto-stat-symbol">{coin.symbol}</div>
                <div className="crypto-stat-price">{formatPrice(coin.price)}</div>
                <div className={`crypto-stat-change ${coin.change >= 0 ? 'positive' : 'negative'}`}>
                  {formatChange(coin.change)}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default CryptoMarketStats;
