import React, { useState } from 'react';
import '../styles/History.css';

const History = ({ user }) => {
  const [activeTab, setActiveTab] = useState('investments');
  const [dateFilter, setDateFilter] = useState('all');

  // Mock data for demonstration
  const investmentHistory = [
    {
      id: 1,
      plan: 'PROMO PLAN',
      amount: 1000,
      dailyReturn: 45,
      totalReturn: 540,
      startDate: '2024-01-15',
      endDate: '2024-01-27',
      status: 'completed',
      daysRemaining: 0
    },
    {
      id: 2,
      plan: 'STANDARD PLAN',
      amount: 500,
      dailyReturn: 10,
      totalReturn: 70,
      startDate: '2024-01-20',
      endDate: '2024-01-27',
      status: 'active',
      daysRemaining: 3
    }
  ];

  const transactionHistory = [
    {
      id: 1,
      type: 'deposit',
      amount: 1500,
      currency: 'USDT',
      status: 'completed',
      date: '2024-01-15',
      txHash: '0x1234...5678'
    },
    {
      id: 2,
      type: 'withdrawal',
      amount: 540,
      currency: 'USDT',
      status: 'completed',
      date: '2024-01-27',
      txHash: '0x8765...4321'
    },
    {
      id: 3,
      type: 'deposit',
      amount: 500,
      currency: 'BTC',
      status: 'pending',
      date: '2024-01-20',
      txHash: '0x9999...1111'
    }
  ];

  const referralHistory = [
    {
      id: 1,
      referredUser: '<EMAIL>',
      commission: 25,
      level: 1,
      date: '2024-01-18',
      status: 'paid'
    },
    {
      id: 2,
      referredUser: '<EMAIL>',
      commission: 15,
      level: 2,
      date: '2024-01-22',
      status: 'pending'
    }
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
      case 'paid':
        return '#00d4aa';
      case 'active':
        return '#f59e0b';
      case 'pending':
        return '#6b7280';
      case 'failed':
        return '#dc2626';
      default:
        return '#6b7280';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const renderInvestmentHistory = () => (
    <div className="history-table">
      <div className="history-table-header">
        <div className="history-col">Plan</div>
        <div className="history-col">Amount</div>
        <div className="history-col">Daily Return</div>
        <div className="history-col">Total Return</div>
        <div className="history-col">Period</div>
        <div className="history-col">Status</div>
        <div className="history-col">Action</div>
      </div>
      <div className="history-table-body">
        {investmentHistory.map((investment) => (
          <div key={investment.id} className="history-table-row">
            <div className="history-col">
              <div className="investment-plan">
                <span className="plan-name">{investment.plan}</span>
              </div>
            </div>
            <div className="history-col">
              <span className="amount">${investment.amount.toLocaleString()}</span>
            </div>
            <div className="history-col">
              <span className="daily-return">${investment.dailyReturn}</span>
            </div>
            <div className="history-col">
              <span className="total-return">${investment.totalReturn}</span>
            </div>
            <div className="history-col">
              <div className="period">
                <div>{formatDate(investment.startDate)}</div>
                <div className="period-separator">→</div>
                <div>{formatDate(investment.endDate)}</div>
              </div>
            </div>
            <div className="history-col">
              <span 
                className="status-badge"
                style={{ backgroundColor: getStatusColor(investment.status) }}
              >
                {investment.status}
                {investment.status === 'active' && (
                  <span className="days-remaining">
                    {investment.daysRemaining} days left
                  </span>
                )}
              </span>
            </div>
            <div className="history-col">
              <button className="action-btn">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="12" cy="12" r="1"/>
                  <circle cx="19" cy="12" r="1"/>
                  <circle cx="5" cy="12" r="1"/>
                </svg>
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderTransactionHistory = () => (
    <div className="history-table">
      <div className="history-table-header">
        <div className="history-col">Type</div>
        <div className="history-col">Amount</div>
        <div className="history-col">Currency</div>
        <div className="history-col">Date</div>
        <div className="history-col">Status</div>
        <div className="history-col">Transaction Hash</div>
      </div>
      <div className="history-table-body">
        {transactionHistory.map((transaction) => (
          <div key={transaction.id} className="history-table-row">
            <div className="history-col">
              <div className="transaction-type">
                <div className={`transaction-icon ${transaction.type}`}>
                  {transaction.type === 'deposit' ? (
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <line x1="12" y1="5" x2="12" y2="19"/>
                      <polyline points="19,12 12,19 5,12"/>
                    </svg>
                  ) : (
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <line x1="12" y1="19" x2="12" y2="5"/>
                      <polyline points="5,12 12,5 19,12"/>
                    </svg>
                  )}
                </div>
                <span className="transaction-type-text">
                  {transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)}
                </span>
              </div>
            </div>
            <div className="history-col">
              <span className="amount">${transaction.amount.toLocaleString()}</span>
            </div>
            <div className="history-col">
              <span className="currency">{transaction.currency}</span>
            </div>
            <div className="history-col">
              <span className="date">{formatDate(transaction.date)}</span>
            </div>
            <div className="history-col">
              <span 
                className="status-badge"
                style={{ backgroundColor: getStatusColor(transaction.status) }}
              >
                {transaction.status}
              </span>
            </div>
            <div className="history-col">
              <div className="tx-hash">
                <span className="tx-hash-text">{transaction.txHash}</span>
                <button className="copy-hash-btn">
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderReferralHistory = () => (
    <div className="history-table">
      <div className="history-table-header">
        <div className="history-col">Referred User</div>
        <div className="history-col">Commission</div>
        <div className="history-col">Level</div>
        <div className="history-col">Date</div>
        <div className="history-col">Status</div>
      </div>
      <div className="history-table-body">
        {referralHistory.map((referral) => (
          <div key={referral.id} className="history-table-row">
            <div className="history-col">
              <span className="referred-user">{referral.referredUser}</span>
            </div>
            <div className="history-col">
              <span className="commission">${referral.commission}</span>
            </div>
            <div className="history-col">
              <span className="level">Level {referral.level}</span>
            </div>
            <div className="history-col">
              <span className="date">{formatDate(referral.date)}</span>
            </div>
            <div className="history-col">
              <span 
                className="status-badge"
                style={{ backgroundColor: getStatusColor(referral.status) }}
              >
                {referral.status}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="history">
      <div className="history-header">
        <h1>Transaction History</h1>
        <p>View all your investments, deposits, withdrawals, and referral earnings</p>
      </div>

      <div className="history-controls">
        <div className="history-tabs">
          <button 
            className={`history-tab ${activeTab === 'investments' ? 'active' : ''}`}
            onClick={() => setActiveTab('investments')}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <line x1="12" y1="1" x2="12" y2="23"/>
              <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
            </svg>
            Investments
          </button>
          <button 
            className={`history-tab ${activeTab === 'transactions' ? 'active' : ''}`}
            onClick={() => setActiveTab('transactions')}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M21 12V7H5a2 2 0 0 1 0-4h14v4"/>
              <path d="M3 5v14a2 2 0 0 0 2 2h16v-5"/>
              <line x1="12" y1="12" x2="12" y2="8"/>
              <line x1="10" y1="10" x2="14" y2="10"/>
            </svg>
            Transactions
          </button>
          <button 
            className={`history-tab ${activeTab === 'referrals' ? 'active' : ''}`}
            onClick={() => setActiveTab('referrals')}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
              <circle cx="8.5" cy="7" r="4"/>
              <path d="M20 8v6"/>
              <path d="M23 11h-6"/>
            </svg>
            Referrals
          </button>
        </div>

        <div className="history-filters">
          <select 
            value={dateFilter} 
            onChange={(e) => setDateFilter(e.target.value)}
            className="date-filter"
          >
            <option value="all">All Time</option>
            <option value="today">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="year">This Year</option>
          </select>
          
          <button className="export-btn">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
              <polyline points="7,10 12,15 17,10"/>
              <line x1="12" y1="15" x2="12" y2="3"/>
            </svg>
            Export
          </button>
        </div>
      </div>

      <div className="history-content">
        {activeTab === 'investments' && renderInvestmentHistory()}
        {activeTab === 'transactions' && renderTransactionHistory()}
        {activeTab === 'referrals' && renderReferralHistory()}
      </div>
    </div>
  );
};

export default History;
